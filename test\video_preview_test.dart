import 'package:flutter_test/flutter_test.dart';
import 'package:my_115_app/models/file_item.dart';
import 'package:my_115_app/services/video_preview_settings_service.dart';

void main() {
  TestWidgetsFlutterBinding.ensureInitialized();
  group('视频预览功能测试', () {
    test('FileItem 应该正确解析 play_long 字段', () {
      // 模拟API返回的JSON数据
      final json = {
        "fid": "3216311166261110757",
        "sha1": "2EF07C959535FB41783AA7954E34844C1A9D5F6C",
        "fn": "BigBuckBunny_320x180.mp4",
        "fs": 64657027,
        "upt": 1753430169,
        "uet": 1753430169,
        "uppt": 1753430169,
        "pc": "d6vhl5jrbveyrxxvm",
        "pid": "3216311019426915498",
        "aid": "1",
        "fta": "1",
        "ism": "0",
        "isp": 0,
        "iss": 0,
        "ic": "0",
        "fc": "1",
        "ico": "mp4",
        "fatr": "",
        "fdesc": "",
        "fvs": 0,
        "fuuid": 334578239,
        "ftype": "196",
        "fflabel": "",
        "isv": 1,
        "fl": [],
        "is_top": 0,
        "current_time": 1,
        "played_end": 0,
        "last_time": "1753430479",
        "def": 0,
        "def2": 100,
        "multitrack": 0,
        "play_long": 596, // 视频时长596秒
        "v_img": "http://115.com/static/plug/vq/mobile/sd.png"
      };

      final fileItem = FileItem.fromJson(json);

      expect(fileItem.playLong, equals(596));
      expect(fileItem.isv, equals(1));
      expect(fileItem.name, equals("BigBuckBunny_320x180.mp4"));
    });

    test('FileItem 应该处理没有 play_long 字段的情况', () {
      final json = {
        "fid": "123",
        "fn": "test.mp4",
        "fc": "1",
        "isv": 1,
        // 没有 play_long 字段
      };

      final fileItem = FileItem.fromJson(json);

      expect(fileItem.playLong, isNull);
      expect(fileItem.isv, equals(1));
    });

    test('VideoPreviewSettings 应该有正确的默认值', () async {
      final settings = await VideoPreviewSettingsService.getSettings();

      expect(settings.minFrameCount, equals(6));
      expect(settings.targetFrameCount, equals(12));
      expect(settings.maxFrameCount, equals(20));
      expect(settings.frameQuality, equals(75));
      expect(settings.frameSize, equals(200));
      expect(settings.animationSpeed, equals('normal'));
    });

    test('VideoPreviewSettings 动画间隔应该正确计算', () async {
      final settings = await VideoPreviewSettingsService.getSettings();

      // 测试不同速度的动画间隔
      final slowSettings = settings.copyWith(animationSpeed: 'slow');
      expect(slowSettings.animationIntervalMs, equals(1000));

      final normalSettings = settings.copyWith(animationSpeed: 'normal');
      expect(normalSettings.animationIntervalMs, equals(600));

      final fastSettings = settings.copyWith(animationSpeed: 'fast');
      expect(fastSettings.animationIntervalMs, equals(300));
    });

    group('帧数计算逻辑测试', () {
      test('短视频应该使用最少帧数', () {
        // 模拟30秒的短视频
        const durationSeconds = 30;
        const minFrameCount = 6;
        const targetFrameCount = 12;
        const maxFrameCount = 20;

        int frameCount;
        if (durationSeconds <= 30) {
          frameCount = minFrameCount;
        } else if (durationSeconds <= 300) {
          frameCount = targetFrameCount;
        } else if (durationSeconds <= 1800) {
          frameCount = (targetFrameCount * 1.5).round();
        } else {
          frameCount = maxFrameCount;
        }

        expect(frameCount, equals(minFrameCount));
      });

      test('中等视频应该使用目标帧数', () {
        // 模拟5分钟的中等视频
        const durationSeconds = 300;
        const minFrameCount = 6;
        const targetFrameCount = 12;
        const maxFrameCount = 20;

        int frameCount;
        if (durationSeconds <= 30) {
          frameCount = minFrameCount;
        } else if (durationSeconds <= 300) {
          frameCount = targetFrameCount;
        } else if (durationSeconds <= 1800) {
          frameCount = (targetFrameCount * 1.5).round();
        } else {
          frameCount = maxFrameCount;
        }

        expect(frameCount, equals(targetFrameCount));
      });

      test('长视频应该使用更多帧数', () {
        // 模拟30分钟的长视频
        const durationSeconds = 1800;
        const minFrameCount = 6;
        const targetFrameCount = 12;
        const maxFrameCount = 20;

        int frameCount;
        if (durationSeconds <= 30) {
          frameCount = minFrameCount;
        } else if (durationSeconds <= 300) {
          frameCount = targetFrameCount;
        } else if (durationSeconds <= 1800) {
          frameCount = (targetFrameCount * 1.5).round();
        } else {
          frameCount = maxFrameCount;
        }

        expect(frameCount, equals((targetFrameCount * 1.5).round()));
      });

      test('超长视频应该使用最大帧数', () {
        // 模拟2小时的超长视频
        const durationSeconds = 7200;
        const minFrameCount = 6;
        const targetFrameCount = 12;
        const maxFrameCount = 20;

        int frameCount;
        if (durationSeconds <= 30) {
          frameCount = minFrameCount;
        } else if (durationSeconds <= 300) {
          frameCount = targetFrameCount;
        } else if (durationSeconds <= 1800) {
          frameCount = (targetFrameCount * 1.5).round();
        } else {
          frameCount = maxFrameCount;
        }

        expect(frameCount, equals(maxFrameCount));
      });
    });

    group('帧位置计算测试', () {
      test('应该避开视频开头和结尾', () {
        const videoDurationMs = 10 * 60 * 1000; // 10分钟
        const frameCount = 12;

        // 计算帧位置（模拟实际算法）
        final startOffset = (2000 < videoDurationMs * 0.05) ? 2000 : videoDurationMs * 0.05;
        final endOffset = (2000 < videoDurationMs * 0.05) ? 2000 : videoDurationMs * 0.05;
        final availableDuration = videoDurationMs - startOffset - endOffset;

        expect(startOffset, equals(2000)); // 应该跳过开头2秒
        expect(endOffset, equals(2000)); // 应该跳过结尾2秒
        expect(availableDuration, equals(videoDurationMs - 4000)); // 可用时长应该减去4秒
      });

      test('短视频应该均匀分布帧位置', () {
        const videoDurationMs = 5 * 1000; // 5秒短视频
        const frameCount = 6;

        // 对于很短的视频，应该均匀分布
        final interval = videoDurationMs / (frameCount + 1);
        final expectedPositions = <int>[];
        for (int i = 1; i <= frameCount; i++) {
          expectedPositions.add((interval * i).round());
        }

        expect(expectedPositions.length, equals(frameCount));
        expect(expectedPositions.first, greaterThan(0));
        expect(expectedPositions.last, lessThan(videoDurationMs));
      });
    });
  });
}

import 'package:flutter/material.dart';
import 'file_list_page.dart';

class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        toolbarHeight: 0, // 隐藏 AppBar
      ),
      body: const FileListPage(
        cid: '0', // 根目录
        title: '我的文件',
      ),
    );
  }
} 
class FileItem {
  final String id;
  final String name;
  final int type; // 0为文件夹，1为文件
  final String? size;
  final String? modifiedTime;
  final String? pickCode;
  final String? thumb; // 缩略图URL
  final String? downloadUrl; // 下载URL
  final List<PathItem> path; // 面包屑路径信息
  final int isv; // 是否为视频文件，1为视频，0为非视频
  final int? playLong; // 视频时长，单位秒

  FileItem({
    required this.id,
    required this.name,
    required this.type,
    this.size,
    this.modifiedTime,
    this.pickCode,
    this.thumb,
    this.downloadUrl,
    this.path = const [],
    this.isv = 0,
    this.playLong,
  });

  factory FileItem.fromJson(Map<String, dynamic> json) {
    // 解析path信息
    List<PathItem> pathItems = [];
    if (json['path'] is List) {
      pathItems = (json['path'] as List)
          .map((pathJson) => PathItem.fromJson(pathJson as Map<String, dynamic>))
          .toList();
    }

    return FileItem(
      id: json['fid']?.toString() ?? json['cid']?.toString() ?? '',
      name: json['fn']?.toString() ?? '',
      type: _parseIntField(json['fc'], 1),
      size: json['fs']?.toString(),
      modifiedTime: json['te']?.toString(),
      pickCode: json['pc']?.toString(),
      thumb: json['thumb']?.toString(),
      downloadUrl: json['uo']?.toString(),
      path: pathItems,
      isv: _parseIntField(json['isv'], 0),
      playLong: json['play_long'] != null ? _parseIntField(json['play_long'], 0) : null,
    );
  }

  /// 安全解析整数字段
  static int _parseIntField(dynamic value, int defaultValue) {
    if (value == null) return defaultValue;
    if (value is int) return value;
    if (value is String) {
      return int.tryParse(value) ?? defaultValue;
    }
    return defaultValue;
  }

  bool get isFolder => type == 0;
  bool get isFile => type == 1;
  bool get isVideo => isv == 1;

  String get formattedSize {
    if (size == null || size == '0') return '';
    
    final bytes = int.tryParse(size!) ?? 0;
    if (bytes == 0) return '';
    
    const units = ['B', 'KB', 'MB', 'GB', 'TB'];
    int unitIndex = 0;
    double fileSize = bytes.toDouble();
    
    while (fileSize >= 1024 && unitIndex < units.length - 1) {
      fileSize /= 1024;
      unitIndex++;
    }
    
    return '${fileSize.toStringAsFixed(fileSize < 10 ? 1 : 0)} ${units[unitIndex]}';
  }
}

/// 路径项，用于面包屑导航
class PathItem {
  final String id;
  final String name;

  PathItem({
    required this.id,
    required this.name,
  });

  factory PathItem.fromJson(Map<String, dynamic> json) {
    return PathItem(
      id: json['fid']?.toString() ?? json['cid']?.toString() ?? '',
      name: json['fn']?.toString() ?? json['name']?.toString() ?? '',
    );
  }
}

/// 排序选项枚举
enum SortOption {
  name,     // 按名称排序
  size,     // 按大小排序
  time,     // 按时间排序
  type,     // 按类型排序
}

/// 排序方向枚举
enum SortDirection {
  asc,      // 升序
  desc,     // 降序
}

/// 排序配置类
class SortConfig {
  final SortOption option;
  final SortDirection direction;

  const SortConfig({
    required this.option,
    required this.direction,
  });

  /// 获取API参数中的排序字段值
  String get apiSortValue {
    switch (option) {
      case SortOption.name:
        return 'file_name';
      case SortOption.size:
        return 'file_size';
      case SortOption.time:
        return 'user_utime';
      case SortOption.type:
        return 'file_type';
    }
  }

  /// 获取API参数中的排序方向值
  String get apiDirectionValue {
    return direction == SortDirection.asc ? '1' : '0';
  }

  /// 获取显示名称
  String get displayName {
    final optionName = switch (option) {
      SortOption.name => '名称',
      SortOption.size => '大小', 
      SortOption.time => '时间',
      SortOption.type => '类型',
    };
    final directionName = direction == SortDirection.asc ? '升序' : '降序';
    return '$optionName ($directionName)';
  }
} 
package com.example.my_115_app

import io.flutter.embedding.android.FlutterActivity
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.plugin.common.MethodChannel
import com.flycc.vodproxy.sdk.Sdk // 导入 gomobile 生成的绑定类

class MainActivity: FlutterActivity() {
    private val CHANNEL = "com.flycc.vodproxy.sdk/vodproxy"

    override fun configureFlutterEngine(flutterEngine: FlutterEngine) {
        super.configureFlutterEngine(flutterEngine)
        MethodChannel(flutterEngine.dartExecutor.binaryMessenger, CHANNEL).setMethodCallHandler {
            call, result ->
            try {
                when (call.method) {
                    "startProxy" -> {
                        // Go 函数需要 int，但 invokeMethod 传递的是 Any?，需要安全转换
                        val port = call.argument<Int>("port") ?: 0 
                        // 调用 Go 的 StartProxyServer
                        val actualPort = Sdk.startProxyServer(port.toLong()) // Go int is 64-bit, so use Long
                        result.success(actualPort)
                    }
                    "stopProxy" -> {
                        // 调用 Go 的 StopProxyServer
                        Sdk.stopProxyServer()
                        result.success(null)
                    }
                    "isServerRunning" -> {
                        // 调用 Go 的 IsServerRunning
                        val isRunning = Sdk.isServerRunning()
                        result.success(isRunning)
                    }
                    "getProxyUrl" -> {
                        val originalUrl = call.argument<String>("originalUrl")
                        if (originalUrl == null) {
                            result.error("INVALID_ARGS", "originalUrl is required", null)
                            return@setMethodCallHandler
                        }
                        val threadCount = call.argument<Int>("threadCount") ?: 2
                        val chunkSize = call.argument<Int>("chunkSize") ?: 10240

                        // 调用 Go 的 GetProxyURL，传递线程数和分片大小参数
                        val proxyUrl = Sdk.getProxyURLWithParams(originalUrl, threadCount.toLong(), chunkSize.toLong())
                        result.success(proxyUrl)
                    }
                    else -> {
                        result.notImplemented()
                    }
                }
            } catch (e: Exception) {
                // gomobile 会将 Go 的 error 转换为 Exception
                result.error("GO_ERROR", e.message, e.stackTraceToString())
            }
        }
    }
}

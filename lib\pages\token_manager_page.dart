import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../services/token_manager.dart';

class TokenManagerPage extends StatefulWidget {
  const TokenManagerPage({super.key});

  @override
  State<TokenManagerPage> createState() => _TokenManagerPageState();
}

class _TokenManagerPageState extends State<TokenManagerPage> {
  final TokenManager _tokenManager = TokenManager();
  final _accessTokenController = TextEditingController();
  final _refreshTokenController = TextEditingController();
  
  bool _isLoading = false;
  bool _isEditing = false;
  Map<String, dynamic>? _tokenStatus;
  String? _accessToken;
  String? _refreshToken;
  DateTime? _tokenExpiry;

  @override
  void initState() {
    super.initState();
    _loadTokenInfo();
  }

  @override
  void dispose() {
    _accessTokenController.dispose();
    _refreshTokenController.dispose();
    super.dispose();
  }

  Future<void> _loadTokenInfo() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final accessToken = await _tokenManager.getAccessToken();
      final refreshToken = await _tokenManager.getRefreshToken();
      final expiry = await _tokenManager.getTokenExpiry();
      final status = await _tokenManager.getTokenStatus();

      setState(() {
        _accessToken = accessToken;
        _refreshToken = refreshToken;
        _tokenExpiry = expiry;
        _tokenStatus = status;
        
        // 设置控制器的值
        _accessTokenController.text = accessToken ?? '';
        _refreshTokenController.text = refreshToken ?? '';
      });
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('加载Token信息失败: $e')),
        );
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _saveTokens() async {
    if (_accessTokenController.text.trim().isEmpty || 
        _refreshTokenController.text.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('请填写完整的Token信息')),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      await _tokenManager.saveTokens(
        accessToken: _accessTokenController.text.trim(),
        refreshToken: _refreshTokenController.text.trim(),
        expiry: DateTime.now().add(const Duration(hours: 1)), // 默认1小时过期
      );

      setState(() {
        _isEditing = false;
      });

      await _loadTokenInfo(); // 重新加载信息

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Token保存成功')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('保存Token失败: $e')),
        );
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _clearTokens() async {
    final bool? shouldClear = await showDialog<bool>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('确认清除'),
          content: const Text('您确定要清除所有Token吗？这将需要重新登录。'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text('取消'),
            ),
            TextButton(
              onPressed: () => Navigator.of(context).pop(true),
              child: const Text('确认', style: TextStyle(color: Colors.red)),
            ),
          ],
        );
      },
    );

    if (shouldClear == true) {
      setState(() {
        _isLoading = true;
      });

      try {
        await _tokenManager.clearTokens();
        await _loadTokenInfo();

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Token已清除')),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('清除Token失败: $e')),
          );
        }
      } finally {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _copyToClipboard(String text, String label) {
    Clipboard.setData(ClipboardData(text: text));
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('$label已复制到剪贴板')),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Token 管理'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        actions: [
          if (!_isEditing && (_accessToken != null || _refreshToken != null))
            IconButton(
              icon: const Icon(Icons.edit),
              onPressed: () => setState(() => _isEditing = true),
              tooltip: '编辑',
            ),
          if (_isEditing)
            IconButton(
              icon: const Icon(Icons.save),
              onPressed: _isLoading ? null : _saveTokens,
              tooltip: '保存',
            ),
          if (_isEditing)
            IconButton(
              icon: const Icon(Icons.cancel),
              onPressed: () {
                setState(() {
                  _isEditing = false;
                  // 恢复原始值
                  _accessTokenController.text = _accessToken ?? '';
                  _refreshTokenController.text = _refreshToken ?? '';
                });
              },
              tooltip: '取消',
            ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildTokenStatusCard(),
                  const SizedBox(height: 16),
                  _buildTokenInputSection(),
                  const SizedBox(height: 24),
                  _buildActionButtons(),
                ],
              ),
            ),
    );
  }

  Widget _buildTokenStatusCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Token 状态',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            if (_tokenStatus != null) ...[
              _buildStatusRow('访问令牌', _tokenStatus!['hasAccessToken'] ? '已设置' : '未设置',
                  _tokenStatus!['hasAccessToken'] ? Colors.green : Colors.red),
              _buildStatusRow('刷新令牌', _tokenStatus!['hasRefreshToken'] ? '已设置' : '未设置',
                  _tokenStatus!['hasRefreshToken'] ? Colors.green : Colors.red),
              if (_tokenExpiry != null)
                _buildStatusRow('过期时间', _formatDateTime(_tokenExpiry!),
                    _tokenStatus!['isExpiringSoon'] ? Colors.orange : Colors.green),
              _buildStatusRow('刷新状态', _tokenStatus!['isRefreshing'] ? '刷新中' : '正常',
                  _tokenStatus!['isRefreshing'] ? Colors.orange : Colors.green),
            ] else
              const Text('加载状态信息中...'),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusRow(String label, String value, Color color) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label, style: const TextStyle(fontWeight: FontWeight.w500)),
          Text(
            value,
            style: TextStyle(color: color, fontWeight: FontWeight.w500),
          ),
        ],
      ),
    );
  }

  Widget _buildTokenInputSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Token 信息',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        _buildTokenField(
          controller: _accessTokenController,
          label: 'Access Token',
          helperText: '115开放平台访问令牌',
          isReadOnly: !_isEditing,
        ),
        const SizedBox(height: 16),
        _buildTokenField(
          controller: _refreshTokenController,
          label: 'Refresh Token',
          helperText: '115开放平台刷新令牌',
          isReadOnly: !_isEditing,
        ),
      ],
    );
  }

  Widget _buildTokenField({
    required TextEditingController controller,
    required String label,
    required String helperText,
    required bool isReadOnly,
  }) {
    return TextFormField(
      controller: controller,
      readOnly: isReadOnly,
      maxLines: isReadOnly ? 1 : 3,
      minLines: 1,
      decoration: InputDecoration(
        labelText: label,
        helperText: helperText,
        border: const OutlineInputBorder(),
        suffixIcon: isReadOnly && controller.text.isNotEmpty
            ? IconButton(
                icon: const Icon(Icons.copy),
                onPressed: () => _copyToClipboard(controller.text, label),
                tooltip: '复制',
              )
            : null,
      ),
      style: isReadOnly
          ? TextStyle(
              fontFamily: 'monospace',
              fontSize: 12,
              color: Theme.of(context).textTheme.bodyMedium?.color?.withOpacity(0.8),
            )
          : null,
    );
  }

  Widget _buildActionButtons() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        if (!_isEditing && (_accessToken == null || _refreshToken == null))
          ElevatedButton.icon(
            onPressed: () => setState(() => _isEditing = true),
            icon: const Icon(Icons.add),
            label: const Text('添加 Token'),
          ),
        if (!_isEditing && (_accessToken != null || _refreshToken != null)) ...[
          ElevatedButton.icon(
            onPressed: _loadTokenInfo,
            icon: const Icon(Icons.refresh),
            label: const Text('刷新状态'),
          ),
          const SizedBox(height: 8),
          OutlinedButton.icon(
            onPressed: _clearTokens,
            icon: const Icon(Icons.delete, color: Colors.red),
            label: const Text('清除 Token', style: TextStyle(color: Colors.red)),
          ),
        ],
      ],
    );
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.year}-${dateTime.month.toString().padLeft(2, '0')}-${dateTime.day.toString().padLeft(2, '0')} '
           '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }
}

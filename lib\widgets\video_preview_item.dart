import 'package:flutter/material.dart';
import 'dart:typed_data';
import 'dart:async';
import 'dart:math' as math;
import '../models/file_item.dart';
import '../utils/file_type_utils.dart';
import '../services/video_preview_settings_service.dart';
import '../services/video_frame_extraction_manager.dart';

/// 视频预览项组件
class VideoPreviewItem extends StatefulWidget {
  final FileItem item;
  final String? playUrl;
  final VoidCallback onTap;
  final bool shouldAutoPlay;
  final VoidCallback? onLoadComplete;
  final VoidCallback? onLoadFailed;

  const VideoPreviewItem({
    super.key,
    required this.item,
    this.playUrl,
    required this.onTap,
    this.shouldAutoPlay = false,
    this.onLoadComplete,
    this.onLoadFailed,
  });

  @override
  State<VideoPreviewItem> createState() => _VideoPreviewItemState();
}

class _VideoPreviewItemState extends State<VideoPreviewItem>
    with TickerProviderStateMixin {
  final List<Uint8List> _frames = [];
  int _currentFrameIndex = 0;
  bool _isLoading = false;
  bool _hasError = false;
  Timer? _frameTimer;
  AnimationController? _animationController;

  // 新增：视频时长和帧提取配置
  int? _videoDurationMs;
  VideoPreviewSettings? _settings;
  List<int> _frameTimePositions = []; // 帧的时间位置

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _initializeSettings();
  }

  /// 初始化设置
  Future<void> _initializeSettings() async {
    _settings = await VideoPreviewSettingsService.getSettings();

    // 从 FileItem 获取真实的视频时长
    if (widget.item.playLong != null && widget.item.playLong! > 0) {
      _videoDurationMs = widget.item.playLong! * 1000; // 转换为毫秒
      debugPrint('从API获取视频时长: ${widget.item.playLong}秒 (${_videoDurationMs}ms)');
    }

    // 延迟到下一帧再开始提取，确保组件已完全构建
    if (widget.shouldAutoPlay && widget.playUrl != null) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          _extractFrames();
        }
      });
    }
  }

  @override
  void didUpdateWidget(VideoPreviewItem oldWidget) {
    super.didUpdateWidget(oldWidget);

    // 延迟到下一帧处理状态变化，避免在构建过程中调用setState
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (!mounted) return;

      // 如果shouldAutoPlay状态改变，相应地提取帧或清理资源
      if (widget.shouldAutoPlay != oldWidget.shouldAutoPlay) {
        if (widget.shouldAutoPlay && widget.playUrl != null) {
          _extractFrames();
        } else {
          _disposeResources();
        }
      }

      // 如果playUrl改变，重新提取帧
      if (widget.playUrl != oldWidget.playUrl && widget.shouldAutoPlay) {
        _disposeResources();
        if (widget.playUrl != null) {
          _extractFrames();
        }
      }
    });
  }

  @override
  void dispose() {
    _disposeResources();
    _animationController?.dispose();
    super.dispose();
  }

  void _disposeResources() {
    _frameTimer?.cancel();
    _frameTimer = null;
    _frames.clear();
    _currentFrameIndex = 0;
  }

  Future<void> _extractFrames() async {
    if (widget.playUrl == null || widget.playUrl!.isEmpty) return;

    setState(() {
      _isLoading = true;
      _hasError = false;
    });

    try {
      // 模拟帧提取过程 - 在实际应用中，这里应该调用视频帧提取API
      // 这里我们先创建一些模拟的帧数据
      await _simulateFrameExtraction();

      if (mounted && _frames.isNotEmpty) {
        setState(() {
          _isLoading = false;
        });

        // 开始帧循环播放
        _startFrameAnimation();

        // 通知加载完成
        widget.onLoadComplete?.call();
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
          _hasError = true;
        });

        // 通知加载失败
        widget.onLoadFailed?.call();
      }
    }
  }

  Future<void> _simulateFrameExtraction() async {
    try {
      // 如果没有从API获取到时长，使用默认时长
      if (_videoDurationMs == null || _videoDurationMs! <= 0) {
        debugPrint('未获取到视频时长，使用默认时长');
        _videoDurationMs = 10 * 60 * 1000; // 默认10分钟
      }

      // 第一步：根据视频时长计算帧提取位置
      _calculateFramePositions();

      // 第二步：提取帧
      await _extractFramesAtPositions();

    } catch (e) {
      debugPrint('帧提取失败: $e');
      // 如果提取失败，使用默认时间点提取少量帧
      await _extractDefaultFrames();
    }
  }





  /// 根据视频时长计算帧提取位置
  void _calculateFramePositions() {
    if (_videoDurationMs == null || _videoDurationMs! <= 0 || _settings == null) {
      // 使用默认位置
      _frameTimePositions = [1000, 3000, 5000, 7000, 9000, 12000];
      return;
    }

    final durationSeconds = _videoDurationMs! / 1000;

    // 根据视频时长动态调整帧数
    int frameCount;
    if (durationSeconds <= 30) {
      frameCount = _settings!.minFrameCount; // 短视频用较少帧
    } else if (durationSeconds <= 300) { // 5分钟以内
      frameCount = _settings!.targetFrameCount;
    } else if (durationSeconds <= 1800) { // 30分钟以内
      frameCount = (_settings!.targetFrameCount * 1.5).round();
    } else {
      frameCount = _settings!.maxFrameCount; // 长视频用更多帧
    }

    frameCount = math.min(frameCount, _settings!.maxFrameCount);
    frameCount = math.max(frameCount, _settings!.minFrameCount);

    // 计算帧的时间位置，避开开头和结尾
    _frameTimePositions.clear();
    final startOffset = math.min(2000, _videoDurationMs! * 0.05); // 开头跳过2秒或5%
    final endOffset = math.min(2000, _videoDurationMs! * 0.05);   // 结尾跳过2秒或5%
    final availableDuration = _videoDurationMs! - startOffset - endOffset;

    if (availableDuration > 0) {
      final interval = availableDuration / (frameCount - 1);

      for (int i = 0; i < frameCount; i++) {
        final timeMs = (startOffset + i * interval).round();
        _frameTimePositions.add(timeMs);
      }
    } else {
      // 如果视频太短，均匀分布
      final interval = _videoDurationMs! / (frameCount + 1);
      for (int i = 1; i <= frameCount; i++) {
        _frameTimePositions.add((interval * i).round());
      }
    }

    debugPrint('计算得到${_frameTimePositions.length}个帧位置: $_frameTimePositions');
  }

  /// 在计算好的位置提取帧
  Future<void> _extractFramesAtPositions() async {
    if (_settings == null) return;

    final frameManager = VideoFrameExtractionManager();

    // 使用批量提取方法，自动应用锁机制和限流
    final extractedFrames = await frameManager.extractFrames(
      videoUrl: widget.playUrl!,
      timePositions: _frameTimePositions,
      maxWidth: _settings!.frameSize,
      maxHeight: _settings!.frameSize,
      quality: _settings!.frameQuality,
      onProgress: (current, total, timeMs) {
        debugPrint('提取进度: $current/$total 帧，时间点: ${timeMs}ms');
      },
      onFrameExtracted: (index, frameData) {
        if (mounted && frameData != null) {
          setState(() {
            _frames.add(frameData);
          });

          // 如果已经提取了足够的帧，可以考虑提前结束
          // 但由于我们使用的是批量提取，这里只是记录进度
          debugPrint('成功提取第${index + 1}帧，当前总数: ${_frames.length}');
        }
      },
    );

    debugPrint('批量帧提取完成，成功提取${extractedFrames.length}帧');
  }

  /// 默认帧提取（当智能提取失败时使用）
  Future<void> _extractDefaultFrames() async {
    final defaultPositions = [1000, 3000, 5000, 7000, 9000, 12000, 15000, 18000];
    final frameManager = VideoFrameExtractionManager();

    // 使用帧提取管理器，自动应用锁机制和限流
    final extractedFrames = await frameManager.extractFrames(
      videoUrl: widget.playUrl!,
      timePositions: defaultPositions,
      maxWidth: 200,
      maxHeight: 200,
      quality: 75,
      onProgress: (current, total, timeMs) {
        debugPrint('默认帧提取进度: $current/$total 帧');
      },
      onFrameExtracted: (index, frameData) {
        if (mounted && frameData != null) {
          setState(() {
            _frames.add(frameData);
          });

          // 至少提取3帧就可以了
          if (_frames.length >= 3) {
            debugPrint('已提取足够的默认帧，停止提取');
            return;
          }
        }
      },
    );

    debugPrint('默认帧提取完成，成功提取${extractedFrames.length}帧');

    // 如果还是没有帧，创建占位符
    if (_frames.isEmpty) {
      for (int i = 0; i < 3; i++) {
        _frames.add(Uint8List(0));
      }
    }
  }

  void _startFrameAnimation() {
    if (_settings == null) return;

    // 使用设置中的动画速度，并根据帧数微调
    int baseIntervalMs = _settings!.animationIntervalMs;

    // 根据帧数微调播放速度
    int intervalMs;
    if (_frames.length <= 6) {
      intervalMs = (baseIntervalMs * 1.2).round(); // 少帧时稍慢一点
    } else if (_frames.length <= 12) {
      intervalMs = baseIntervalMs; // 中等帧数使用基础速度
    } else {
      intervalMs = (baseIntervalMs * 0.8).round(); // 多帧时稍快一点，让预览更流畅
    }

    _frameTimer = Timer.periodic(Duration(milliseconds: intervalMs), (timer) {
      if (mounted && _frames.isNotEmpty) {
        setState(() {
          _currentFrameIndex = (_currentFrameIndex + 1) % _frames.length;
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: widget.onTap,
      child: Container(
        decoration: BoxDecoration(
          color: Theme.of(context).cardColor,
          borderRadius: BorderRadius.circular(8),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          children: [
            Expanded(
              child: ClipRRect(
                borderRadius: const BorderRadius.vertical(top: Radius.circular(8)),
                child: _buildPreviewContent(),
              ),
            ),
            Container(
              padding: const EdgeInsets.all(8),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    widget.item.name,
                    style: const TextStyle(fontSize: 12, fontWeight: FontWeight.w500),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  if (widget.item.formattedSize.isNotEmpty) ...[
                    const SizedBox(height: 2),
                    Text(
                      widget.item.formattedSize,
                      style: TextStyle(
                        fontSize: 10,
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPreviewContent() {
    // 如果正在加载视频
    if (_isLoading) {
      return Container(
        color: Theme.of(context).brightness == Brightness.dark 
          ? Colors.grey[850] 
          : Colors.grey[100],
        child: const Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CircularProgressIndicator(strokeWidth: 2),
              SizedBox(height: 8),
              Text(
                '加载中...',
                style: TextStyle(fontSize: 10),
              ),
            ],
          ),
        ),
      );
    }

    // 如果视频加载失败
    if (_hasError) {
      return Container(
        color: Theme.of(context).brightness == Brightness.dark 
          ? Colors.red[900] 
          : Colors.red[50],
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.error_outline,
                color: Colors.red[400],
                size: 32,
              ),
              const SizedBox(height: 4),
              const Text(
                '加载失败',
                style: TextStyle(fontSize: 10),
              ),
            ],
          ),
        ),
      );
    }

    // 如果帧已提取，显示帧预览
    if (_frames.isNotEmpty) {
      return Stack(
        fit: StackFit.expand,
        children: [
          // 显示当前帧
          _frames[_currentFrameIndex].isNotEmpty
              ? ClipRRect(
                  borderRadius: BorderRadius.circular(8),
                  child: Image.memory(
                    _frames[_currentFrameIndex],
                    fit: BoxFit.cover,
                    width: double.infinity,
                    height: double.infinity,
                  ),
                )
              : Container(
                  decoration: BoxDecoration(
                    color: Colors.grey[300],
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.video_library,
                          size: 32,
                          color: Colors.grey[600],
                        ),
                        const SizedBox(height: 4),
                        Text(
                          '帧 ${_currentFrameIndex + 1}/${_frames.length}',
                          style: TextStyle(
                            color: Colors.grey[600],
                            fontSize: 10,
                          ),
                        ),
                        if (_videoDurationMs != null) ...[
                          const SizedBox(height: 2),
                          Text(
                            '时长: ${_formatDuration(_videoDurationMs!)}',
                            style: TextStyle(
                              color: Colors.grey[600],
                              fontSize: 8,
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),
                ),
          // 播放指示器
          Positioned(
            top: 8,
            right: 8,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
              decoration: BoxDecoration(
                color: Colors.black.withValues(alpha: 0.7),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Icon(
                    Icons.play_arrow,
                    color: Colors.white,
                    size: 12,
                  ),
                  const SizedBox(width: 2),
                  Text(
                    '${_frames.length}帧',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 8,
                    ),
                  ),
                ],
              ),
            ),
          ),
          // 进度指示器（显示当前帧在视频中的位置）
          if (_frameTimePositions.isNotEmpty && _currentFrameIndex < _frameTimePositions.length)
            Positioned(
              bottom: 8,
              left: 8,
              right: 8,
              child: Container(
                height: 2,
                decoration: BoxDecoration(
                  color: Colors.black.withValues(alpha: 0.3),
                  borderRadius: BorderRadius.circular(1),
                ),
                child: FractionallySizedBox(
                  alignment: Alignment.centerLeft,
                  widthFactor: _videoDurationMs != null && _videoDurationMs! > 0
                      ? (_frameTimePositions[_currentFrameIndex] / _videoDurationMs!)
                      : (_currentFrameIndex + 1) / _frames.length,
                  child: Container(
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.8),
                      borderRadius: BorderRadius.circular(1),
                    ),
                  ),
                ),
              ),
            ),
        ],
      );
    }

    // 默认显示缩略图或视频图标
    if (widget.item.thumb != null && widget.item.thumb!.isNotEmpty) {
      return Stack(
        fit: StackFit.expand,
        children: [
          Image.network(
            widget.item.thumb!,
            fit: BoxFit.cover,
            errorBuilder: (context, error, stackTrace) => _buildVideoIcon(),
          ),
          // 播放按钮覆盖层
          Center(
            child: Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.black.withValues(alpha: 0.5),
                shape: BoxShape.circle,
              ),
              child: const Icon(
                Icons.play_arrow,
                color: Colors.white,
                size: 24,
              ),
            ),
          ),
        ],
      );
    }

    // 没有缩略图时显示视频图标
    return _buildVideoIcon();
  }

  Widget _buildVideoIcon() {
    return Container(
      color: Theme.of(context).brightness == Brightness.dark
        ? Colors.grey[850]
        : Colors.grey[100],
      child: Center(
        child: FileTypeUtils.buildFileIcon(
          widget.item,
          size: 48,
          context: context,
        ),
      ),
    );
  }

  /// 格式化时长显示
  String _formatDuration(int durationMs) {
    final duration = Duration(milliseconds: durationMs);
    final hours = duration.inHours;
    final minutes = duration.inMinutes.remainder(60);
    final seconds = duration.inSeconds.remainder(60);

    if (hours > 0) {
      return '$hours:${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
    } else {
      return '$minutes:${seconds.toString().padLeft(2, '0')}';
    }
  }
}

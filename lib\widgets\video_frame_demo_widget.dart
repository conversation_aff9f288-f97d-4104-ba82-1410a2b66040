import 'package:flutter/material.dart';
import 'dart:async';
import '../services/video_frame_extraction_manager.dart';

/// 视频帧提取演示组件
/// 展示锁机制和限流功能的实际效果
class VideoFrameDemoWidget extends StatefulWidget {
  const VideoFrameDemoWidget({super.key});

  @override
  State<VideoFrameDemoWidget> createState() => _VideoFrameDemoWidgetState();
}

class _VideoFrameDemoWidgetState extends State<VideoFrameDemoWidget> {
  final VideoFrameExtractionManager _frameManager = VideoFrameExtractionManager();
  Timer? _statusTimer;
  Map<String, dynamic> _statistics = {};
  bool _isRunningDemo = false;

  @override
  void initState() {
    super.initState();
    _startStatusUpdates();
  }

  @override
  void dispose() {
    _statusTimer?.cancel();
    super.dispose();
  }

  void _startStatusUpdates() {
    _statusTimer = Timer.periodic(const Duration(milliseconds: 500), (timer) {
      if (mounted) {
        setState(() {
          _statistics = _frameManager.getStatistics();
        });
      }
    });
  }

  /// 演示锁机制：同时启动多个视频的帧提取
  Future<void> _demonstrateLockMechanism() async {
    if (_isRunningDemo) return;
    
    setState(() {
      _isRunningDemo = true;
    });

    try {
      // 模拟3个不同的视频URL
      final videoUrls = [
        'https://example.com/video1.mp4',
        'https://example.com/video2.mp4', 
        'https://example.com/video3.mp4',
      ];

      // 同时启动3个视频的帧提取任务
      final futures = videoUrls.map((url) => _extractFramesForVideo(url));
      
      // 等待所有任务完成
      await Future.wait(futures);
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('锁机制演示完成！注意只有一个视频能同时提取帧'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isRunningDemo = false;
        });
      }
    }
  }

  /// 为单个视频提取帧
  Future<void> _extractFramesForVideo(String videoUrl) async {
    final timePositions = [1000, 3000, 5000, 7000, 9000]; // 5个时间点
    
    await _frameManager.extractFrames(
      videoUrl: videoUrl,
      timePositions: timePositions,
      maxWidth: 200,
      maxHeight: 200,
      quality: 75,
      onProgress: (current, total, timeMs) {
        debugPrint('[$videoUrl] 进度: $current/$total 帧');
      },
      onFrameExtracted: (index, frameData) {
        debugPrint('[$videoUrl] 第${index + 1}帧提取${frameData != null ? '成功' : '失败'}');
      },
    );
  }

  /// 演示限流机制：快速连续提取帧
  Future<void> _demonstrateRateLimiting() async {
    if (_isRunningDemo) return;
    
    setState(() {
      _isRunningDemo = true;
    });

    try {
      final videoUrl = 'https://example.com/demo-video.mp4';
      final timePositions = [1000, 1500, 2000, 2500, 3000, 3500, 4000, 4500];
      
      final startTime = DateTime.now();
      
      // 连续提取多帧，观察限流效果
      for (int i = 0; i < timePositions.length; i++) {
        final frameStartTime = DateTime.now();
        
        await _frameManager.extractFrame(
          videoUrl: videoUrl,
          timeMs: timePositions[i],
          maxWidth: 200,
          maxHeight: 200,
          quality: 75,
        );
        
        final frameEndTime = DateTime.now();
        final frameDuration = frameEndTime.difference(frameStartTime);
        
        debugPrint('第${i + 1}帧提取耗时: ${frameDuration.inMilliseconds}ms');
      }
      
      final totalTime = DateTime.now().difference(startTime);
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('限流演示完成！总耗时: ${totalTime.inSeconds}秒'),
            backgroundColor: Colors.blue,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isRunningDemo = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.all(16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              '视频帧提取控制演示',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 16),
            
            // 实时状态显示
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surfaceVariant,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '实时状态',
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      Icon(
                        _statistics['isCurrentlyExtracting'] == true 
                            ? Icons.play_circle_filled 
                            : Icons.pause_circle_filled,
                        color: _statistics['isCurrentlyExtracting'] == true 
                            ? Colors.green 
                            : Colors.grey,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        _statistics['isCurrentlyExtracting'] == true 
                            ? '正在提取帧' 
                            : '空闲状态',
                        style: Theme.of(context).textTheme.bodyMedium,
                      ),
                    ],
                  ),
                  if (_statistics['currentVideo'] != null) ...[
                    const SizedBox(height: 4),
                    Text(
                      '当前视频: ${_statistics['currentVideo']}',
                      style: Theme.of(context).textTheme.bodySmall,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                  const SizedBox(height: 8),
                  Wrap(
                    spacing: 16,
                    children: [
                      _buildStatChip('总提取', _statistics['totalExtractions']?.toString() ?? '0'),
                      _buildStatChip('成功', _statistics['successfulExtractions']?.toString() ?? '0'),
                      _buildStatChip('失败', _statistics['failedExtractions']?.toString() ?? '0'),
                      _buildStatChip('成功率', _statistics['successRate']?.toString() ?? '0%'),
                    ],
                  ),
                ],
              ),
            ),
            
            const SizedBox(height: 16),
            
            // 演示按钮
            Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                ElevatedButton.icon(
                  onPressed: _isRunningDemo ? null : _demonstrateLockMechanism,
                  icon: const Icon(Icons.lock),
                  label: const Text('演示锁机制'),
                ),
                const SizedBox(height: 8),
                ElevatedButton.icon(
                  onPressed: _isRunningDemo ? null : _demonstrateRateLimiting,
                  icon: const Icon(Icons.speed),
                  label: const Text('演示限流机制'),
                ),
                const SizedBox(height: 8),
                OutlinedButton.icon(
                  onPressed: () {
                    _frameManager.resetStatistics();
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(content: Text('统计信息已重置')),
                    );
                  },
                  icon: const Icon(Icons.refresh),
                  label: const Text('重置统计'),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // 说明文字
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.primaryContainer.withOpacity(0.3),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: Theme.of(context).colorScheme.primary.withOpacity(0.3),
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '功能说明',
                    style: Theme.of(context).textTheme.titleSmall?.copyWith(
                      color: Theme.of(context).colorScheme.primary,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    '• 锁机制：确保同一时间只能有一个视频在提取帧\n'
                    '• 限流机制：每秒最多提取2帧，避免过度占用资源\n'
                    '• 自动排队：多个提取请求会自动排队等待\n'
                    '• 实时监控：可以查看当前提取状态和统计信息',
                    style: Theme.of(context).textTheme.bodySmall,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatChip(String label, String value) {
    return Chip(
      label: Text('$label: $value'),
      backgroundColor: Theme.of(context).colorScheme.secondaryContainer,
      labelStyle: Theme.of(context).textTheme.bodySmall,
    );
  }
}

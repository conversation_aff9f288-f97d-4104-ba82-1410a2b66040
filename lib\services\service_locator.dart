import 'package:dio/dio.dart';
import 'package:get_it/get_it.dart';
import 'package:dio_cache_interceptor/dio_cache_interceptor.dart';
import 'package:my_115_app/services/throttler_interceptor.dart';

// 导入服务
import 'token_manager.dart';
import 'talker_service.dart';
import 'vod_proxy_settings_service.dart';
import 'multi_video_settings_service.dart';

import 'auth_interceptor.dart';

/// 服务定位器配置
class ServiceLocator {
  static final GetIt _getIt = GetIt.instance;
  
  /// 获取GetIt实例
  static GetIt get instance => _getIt;

  /// 初始化所有服务
  static Future<void> setupServices() async {
    // 核心服务 - 单例
    _getIt.registerSingleton<TalkerService>(TalkerService());
    _getIt.registerSingleton<TokenManager>(TokenManager());
    _getIt.registerSingleton<VodProxySettingsService>(VodProxySettingsService());
    _getIt.registerSingleton<MultiVideoSettingsService>(MultiVideoSettingsService());

    // 初始化TalkerService
    _getIt<TalkerService>().initialize();

    // 初始化VodProxySettingsService
    await _getIt<VodProxySettingsService>().initialize();

    // 初始化MultiVideoSettingsService
    await _getIt<MultiVideoSettingsService>().initialize();

    // 拦截器 - 工厂模式（可能需要多个实例）
    _getIt.registerFactory<AuthInterceptor>(() => AuthInterceptor());
    _getIt.registerFactory<RetryInterceptor>(() => RetryInterceptor());
    _getIt.registerFactory<DioThrottler>(() => DioThrottler(const Duration(milliseconds: 500)));

    // Dio实例 - 单例，配置了拦截器
    _getIt.registerSingleton<Dio>(_createDioInstance());

    // 初始化TokenManager
    _getIt<TokenManager>().initialize();
  }

  /// 创建配置好的Dio实例
  static Dio _createDioInstance() {
    final dio = Dio(BaseOptions(
      baseUrl: 'https://proapi.115.com',
      connectTimeout: const Duration(seconds: 15),
      receiveTimeout: const Duration(seconds: 30),
      sendTimeout: const Duration(seconds: 15),
      headers: {
        'User-Agent': 'My115App/1.0.0 (Flutter Client)',
        'Accept': 'application/json',
      },
      // 启用gzip压缩
      extra: {
        'withCredentials': false,
      },
    ));

    // 配置缓存选项
    final cacheOptions = CacheOptions(
      // 使用内存缓存存储
      store: MemCacheStore(),
      // 缓存策略：优先使用缓存，如果缓存不存在或过期则请求网络
      policy: CachePolicy.request,
      // 缓存最大保存时间（覆盖HTTP指令）
      maxStale: const Duration(days: 7),
      // 缓存优先级
      priority: CachePriority.normal,
      // 自定义缓存键生成器，包含查询参数
      keyBuilder: CacheOptions.defaultCacheKeyBuilder,
      // 不缓存POST请求
      allowPostMethod: false,
    );

    // 添加拦截器（顺序很重要）
    dio.interceptors.addAll([
      // 1. 缓存拦截器 - 最先处理缓存
      DioCacheInterceptor(options: cacheOptions),
      
      // 2. TalkerDioLogger - 现代化日志记录
      _getIt<TalkerService>().dioLogger,
      
      // 3. 认证拦截器 - 处理token
      _getIt<AuthInterceptor>(),
      
      // 4. 重试拦截器 - 处理网络错误
      _getIt<RetryInterceptor>(),

      // 5. 速率限制拦截器 - 限制请求速率
      _getIt<DioThrottler>(),
    ]);

    return dio;
  }

  /// 重置服务（主要用于测试）
  static Future<void> reset() async {
    await _getIt.reset();
  }

  /// 检查服务是否已注册
  static bool isRegistered<T extends Object>() {
    return _getIt.isRegistered<T>();
  }

  /// 获取服务实例
  static T get<T extends Object>() {
    return _getIt<T>();
  }

  /// 注册新服务（动态注册）
  static void registerSingleton<T extends Object>(T instance) {
    if (!_getIt.isRegistered<T>()) {
      _getIt.registerSingleton<T>(instance);
    }
  }

  /// 注册工厂服务
  static void registerFactory<T extends Object>(T Function() factory) {
    if (!_getIt.isRegistered<T>()) {
      _getIt.registerFactory<T>(factory);
    }
  }

  /// 注销服务
  static Future<void> unregister<T extends Object>() async {
    if (_getIt.isRegistered<T>()) {
      await _getIt.unregister<T>();
    }
  }
}

/// 服务定位器扩展 - 提供便捷的访问方法
extension ServiceLocatorExtension on GetIt {
  /// 获取TokenManager
  TokenManager get tokenManager => get<TokenManager>();

  /// 获取TalkerService
  TalkerService get talkerService => get<TalkerService>();

  /// 获取VodProxySettingsService
  VodProxySettingsService get vodProxySettings => get<VodProxySettingsService>();

  /// 获取MultiVideoSettingsService
  MultiVideoSettingsService get multiVideoSettings => get<MultiVideoSettingsService>();

  /// 获取Dio实例
  Dio get dio => get<Dio>();
}

/// 全局服务定位器实例
final serviceLocator = ServiceLocator.instance; 
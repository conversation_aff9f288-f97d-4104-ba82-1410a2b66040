import 'package:flutter/material.dart';
import '../models/file_item.dart';

class SortDialog extends StatefulWidget {
  final SortConfig currentSort;

  const SortDialog({
    super.key,
    required this.currentSort,
  });

  @override
  State<SortDialog> createState() => _SortDialogState();
}

class _SortDialogState extends State<SortDialog> {
  late SortOption _selectedOption;
  late SortDirection _selectedDirection;

  @override
  void initState() {
    super.initState();
    _selectedOption = widget.currentSort.option;
    _selectedDirection = widget.currentSort.direction;
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('排序方式'),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '排序字段:',
            style: TextStyle(fontWeight: FontWeight.w500),
          ),
          const SizedBox(height: 8),
          ..._buildSortOptions(),
          const SizedBox(height: 16),
          const Text(
            '排序方向:',
            style: TextStyle(fontWeight: FontWeight.w500),
          ),
          const SizedBox(height: 8),
          ..._buildDirectionOptions(),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('取消'),
        ),
        TextButton(
          onPressed: () {
            final newSort = SortConfig(
              option: _selectedOption,
              direction: _selectedDirection,
            );
            Navigator.of(context).pop(newSort);
          },
          child: const Text('确定'),
        ),
      ],
    );
  }

  List<Widget> _buildSortOptions() {
    return SortOption.values.map((option) {
      final title = switch (option) {
        SortOption.name => '文件名',
        SortOption.size => '文件大小',
        SortOption.time => '修改时间',
        SortOption.type => '文件类型',
      };

      return RadioListTile<SortOption>(
        title: Text(title),
        value: option,
        groupValue: _selectedOption,
        onChanged: (value) {
          if (value != null) {
            setState(() {
              _selectedOption = value;
            });
          }
        },
        dense: true,
        contentPadding: EdgeInsets.zero,
      );
    }).toList();
  }

  List<Widget> _buildDirectionOptions() {
    return SortDirection.values.map((direction) {
      final title = switch (direction) {
        SortDirection.asc => '升序',
        SortDirection.desc => '降序',
      };

      return RadioListTile<SortDirection>(
        title: Text(title),
        value: direction,
        groupValue: _selectedDirection,
        onChanged: (value) {
          if (value != null) {
            setState(() {
              _selectedDirection = value;
            });
          }
        },
        dense: true,
        contentPadding: EdgeInsets.zero,
      );
    }).toList();
  }
}

/// 显示排序对话框的便捷方法
Future<SortConfig?> showSortDialog(
  BuildContext context,
  SortConfig currentSort,
) {
  return showDialog<SortConfig>(
    context: context,
    builder: (context) => SortDialog(currentSort: currentSort),
  );
} 
import 'package:flutter_test/flutter_test.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:my_115_app/services/vod_proxy_settings_service.dart';

void main() {
  group('VodProxySettingsService', () {
    late VodProxySettingsService service;

    setUp(() async {
      // 清除SharedPreferences
      SharedPreferences.setMockInitialValues({});
      service = VodProxySettingsService();
      // 重置服务状态
      service.resetForTesting();
    });

    test('初始化时应该使用默认值', () async {
      await service.initialize();

      expect(service.isEnabled, false);
      expect(service.port, 37150);
      expect(service.threadCount, 2);
      expect(service.chunkSize, 10240);
      expect(service.isInitialized, true);
      expect(service.settingsSummary, '已禁用');
    });

    test('应该能够设置启用状态', () async {
      await service.initialize();

      await service.setEnabled(true);

      expect(service.isEnabled, true);
      expect(service.settingsSummary, '已启用 (端口: 37150, 线程: 2, 分片: 10240KB)');
    });

    test('应该能够设置端口', () async {
      await service.initialize();

      await service.setPort(8080);

      expect(service.port, 8080);
    });

    test('应该拒绝无效端口', () async {
      await service.initialize();
      final originalPort = service.port;

      await service.setPort(0);
      expect(service.port, originalPort);

      await service.setPort(70000);
      expect(service.port, originalPort);
    });

    test('应该能够设置线程数', () async {
      await service.initialize();

      await service.setThreadCount(4);

      expect(service.threadCount, 4);
    });

    test('应该拒绝无效线程数', () async {
      await service.initialize();
      final originalThreadCount = service.threadCount;

      await service.setThreadCount(0);
      expect(service.threadCount, originalThreadCount);

      await service.setThreadCount(50);
      expect(service.threadCount, originalThreadCount);
    });

    test('应该能够设置分片大小', () async {
      await service.initialize();

      await service.setChunkSize(2048);

      expect(service.chunkSize, 2048);
    });

    test('应该拒绝无效分片大小', () async {
      await service.initialize();
      final originalChunkSize = service.chunkSize;

      await service.setChunkSize(500);
      expect(service.chunkSize, originalChunkSize);

      await service.setChunkSize(200000);
      expect(service.chunkSize, originalChunkSize);
    });

    test('应该能够重置为默认值', () async {
      await service.initialize();
      
      // 修改所有设置
      await service.setEnabled(true);
      await service.setPort(8080);
      await service.setThreadCount(4);
      await service.setChunkSize(2048);

      // 重置
      await service.resetToDefaults();

      expect(service.isEnabled, false);
      expect(service.port, 37150);
      expect(service.threadCount, 2);
      expect(service.chunkSize, 10240);
    });

    test('应该持久化设置', () async {
      await service.initialize();
      
      // 修改设置
      await service.setEnabled(true);
      await service.setPort(8080);
      await service.setThreadCount(4);
      await service.setChunkSize(2048);

      // 创建新实例并初始化
      final newService = VodProxySettingsService();
      newService.resetForTesting();
      await newService.initialize();

      // 验证设置被持久化
      expect(newService.isEnabled, true);
      expect(newService.port, 8080);
      expect(newService.threadCount, 4);
      expect(newService.chunkSize, 2048);
    });

    test('初始化失败时应该使用默认值', () async {
      // 模拟SharedPreferences异常
      SharedPreferences.setMockInitialValues({});
      
      await service.initialize();

      expect(service.isEnabled, false);
      expect(service.port, 37150);
      expect(service.threadCount, 2);
      expect(service.chunkSize, 10240);
      expect(service.isInitialized, true);
    });

    test('重复初始化应该被忽略', () async {
      await service.initialize();
      expect(service.isInitialized, true);

      // 再次初始化
      await service.initialize();
      expect(service.isInitialized, true);
    });
  });
}

enum Environment {
  development,
  production,
}

class EnvironmentConfig {
  static Environment _environment = Environment.production;
  
  static Environment get environment => _environment;
  
  static void setEnvironment(Environment env) {
    _environment = env;
  }
  
  static bool get isDevelopment => _environment == Environment.development;
  static bool get isProduction => _environment == Environment.production;
  
  // API配置
  static String get apiBaseUrl {
    switch (_environment) {
      case Environment.development:
        return 'https://dev-api.example.com';
      case Environment.production:
        return 'https://api.example.com';
    }
  }
  
  // 应用名称
  static String get appName {
    switch (_environment) {
      case Environment.development:
        return 'My 115 App (Dev)';
      case Environment.production:
        return 'My 115 App';
    }
  }
  
  // 包名后缀
  static String get packageSuffix {
    switch (_environment) {
      case Environment.development:
        return '.dev';
      case Environment.production:
        return '';
    }
  }
  
  // 日志级别
  static bool get enableDebugLogs {
    switch (_environment) {
      case Environment.development:
        return true;
      case Environment.production:
        return false;
    }
  }
  
  // 其他环境相关配置
  static Duration get networkTimeout {
    switch (_environment) {
      case Environment.development:
        return const Duration(seconds: 30);
      case Environment.production:
        return const Duration(seconds: 15);
    }
  }
}

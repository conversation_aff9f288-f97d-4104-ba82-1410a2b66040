import 'dart:async';
import 'dart:typed_data';
import 'package:flutter/foundation.dart';
import 'package:get_thumbnail_video/index.dart';
import 'package:get_thumbnail_video/video_thumbnail.dart';
import 'package:mutex/mutex.dart';
import '../services/token_manager.dart';

/// 视频帧提取管理器
/// 实现全局锁机制和限流控制，确保同一时间只能提取一个视频，且每秒最多提取2帧
class VideoFrameExtractionManager {
  static final VideoFrameExtractionManager _instance = VideoFrameExtractionManager._internal();
  factory VideoFrameExtractionManager() => _instance;
  VideoFrameExtractionManager._internal();

  // 全局锁，确保同一时间只能有一个视频在提取帧
  final Mutex _globalLock = Mutex();
  
  // 限流控制：每秒最多2帧
  static const int _maxFramesPerSecond = 2;
  static const Duration _frameInterval = Duration(milliseconds: 500); // 500ms间隔 = 每秒2帧
  
  // 记录最后一次帧提取的时间
  DateTime _lastFrameTime = DateTime.now().subtract(const Duration(seconds: 1));
  
  // 当前正在提取的视频URL（用于调试和状态监控）
  String? _currentExtractionVideo;
  
  // 提取统计信息
  int _totalExtractions = 0;
  int _successfulExtractions = 0;
  int _failedExtractions = 0;

  /// 提取单个视频帧
  /// 
  /// [videoUrl] 视频URL
  /// [timeMs] 提取时间点（毫秒）
  /// [maxWidth] 最大宽度
  /// [maxHeight] 最大高度
  /// [quality] 图片质量 (1-100)
  /// [onProgress] 进度回调（可选）
  /// 
  /// 返回提取的帧数据，如果失败返回null
  Future<Uint8List?> extractFrame({
    required String videoUrl,
    required int timeMs,
    required int maxWidth,
    required int maxHeight,
    required int quality,
    void Function(String videoUrl, int timeMs)? onProgress,
  }) async {
    return await _globalLock.protect(() async {
      try {
        _currentExtractionVideo = videoUrl;
        _totalExtractions++;
        
        // 限流控制：确保帧提取间隔不少于500ms
        final now = DateTime.now();
        final timeSinceLastFrame = now.difference(_lastFrameTime);
        
        if (timeSinceLastFrame < _frameInterval) {
          final waitTime = _frameInterval - timeSinceLastFrame;
          debugPrint('帧提取限流：等待 ${waitTime.inMilliseconds}ms');
          await Future.delayed(waitTime);
        }
        
        // 更新最后提取时间
        _lastFrameTime = DateTime.now();
        
        // 调用进度回调
        onProgress?.call(videoUrl, timeMs);
        
        debugPrint('开始提取帧：$videoUrl 时间点：${timeMs}ms');
        
        // 实际提取帧
        final uint8list = await VideoThumbnail.thumbnailData(
          video: videoUrl,
          imageFormat: ImageFormat.JPEG,
          maxWidth: maxWidth,
          maxHeight: maxHeight,
          timeMs: timeMs,
          quality: quality,
          headers: {
            'User-Agent': TokenManager.userAgent,
          },
        );
        
        if (uint8list != null && uint8list.isNotEmpty) {
          _successfulExtractions++;
          debugPrint('帧提取成功：${uint8list.length} bytes');
          return uint8list;
        } else {
          _failedExtractions++;
          debugPrint('帧提取失败：返回数据为空');
          return null;
        }
        
      } catch (e) {
        _failedExtractions++;
        debugPrint('帧提取异常：$e');
        return null;
      } finally {
        _currentExtractionVideo = null;
      }
    });
  }

  /// 批量提取视频帧
  /// 
  /// [videoUrl] 视频URL
  /// [timePositions] 时间点列表（毫秒）
  /// [maxWidth] 最大宽度
  /// [maxHeight] 最大高度
  /// [quality] 图片质量 (1-100)
  /// [onProgress] 进度回调（当前帧索引，总帧数，时间点）
  /// [onFrameExtracted] 单帧提取完成回调（帧索引，帧数据）
  /// 
  /// 返回成功提取的帧数据列表
  Future<List<Uint8List>> extractFrames({
    required String videoUrl,
    required List<int> timePositions,
    required int maxWidth,
    required int maxHeight,
    required int quality,
    void Function(int current, int total, int timeMs)? onProgress,
    void Function(int index, Uint8List? frameData)? onFrameExtracted,
  }) async {
    return await _globalLock.protect(() async {
      final List<Uint8List> frames = [];
      _currentExtractionVideo = videoUrl;
      
      debugPrint('开始批量提取帧：$videoUrl，共${timePositions.length}帧');
      
      try {
        for (int i = 0; i < timePositions.length; i++) {
          final timeMs = timePositions[i];
          
          // 进度回调
          onProgress?.call(i + 1, timePositions.length, timeMs);
          
          // 提取单帧（这里会自动应用限流）
          final frameData = await extractFrame(
            videoUrl: videoUrl,
            timeMs: timeMs,
            maxWidth: maxWidth,
            maxHeight: maxHeight,
            quality: quality,
          );
          
          // 单帧提取完成回调
          onFrameExtracted?.call(i, frameData);
          
          if (frameData != null) {
            frames.add(frameData);
          }
        }
        
        debugPrint('批量帧提取完成：成功${frames.length}/${timePositions.length}帧');
        return frames;
        
      } finally {
        _currentExtractionVideo = null;
      }
    });
  }

  /// 检查是否有视频正在提取帧
  bool get isExtracting => _globalLock.isLocked;

  /// 获取当前正在提取的视频URL
  String? get currentExtractionVideo => _currentExtractionVideo;

  /// 获取提取统计信息
  Map<String, dynamic> getStatistics() {
    return {
      'totalExtractions': _totalExtractions,
      'successfulExtractions': _successfulExtractions,
      'failedExtractions': _failedExtractions,
      'successRate': _totalExtractions > 0 
          ? (_successfulExtractions / _totalExtractions * 100).toStringAsFixed(1) + '%'
          : '0%',
      'isCurrentlyExtracting': isExtracting,
      'currentVideo': _currentExtractionVideo,
      'maxFramesPerSecond': _maxFramesPerSecond,
      'frameIntervalMs': _frameInterval.inMilliseconds,
    };
  }

  /// 重置统计信息
  void resetStatistics() {
    _totalExtractions = 0;
    _successfulExtractions = 0;
    _failedExtractions = 0;
  }

  /// 强制释放锁（仅在紧急情况下使用）
  /// 注意：这可能会导致正在进行的提取操作出现异常
  void forceReleaseLock() {
    debugPrint('警告：强制释放视频帧提取锁');
    _currentExtractionVideo = null;
    // Mutex 类没有直接的强制释放方法，这里只是清理状态
    // 实际的锁会在当前操作完成后自动释放
  }
}

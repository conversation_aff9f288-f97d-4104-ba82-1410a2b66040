import 'dart:async';
import 'dart:convert';
import 'package:dio/dio.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:get_it/get_it.dart';
import 'talker_service.dart';

/// Token管理器 - 使用安全存储和更健壮的设计
class TokenManager {
  static final TokenManager _instance = TokenManager._internal();
  factory TokenManager() => _instance;
  TokenManager._internal();

  static const String refreshTokenUrl = 'https://passportapi.115.com/open/refreshToken';
  static const String userAgent = 'My115App/1.0.0 (Flutter Client)';
  
  // 安全存储密钥
  static const String _accessTokenKey = 'open_access_token';
  static const String _refreshTokenKey = 'open_refresh_token';
  static const String _tokenExpiryKey = 'token_expiry';
  static const String _userInfoKey = 'user_info';
  
  // 安全存储实例
  static const FlutterSecureStorage _storage = FlutterSecureStorage(
    aOptions: AndroidOptions(
      encryptedSharedPreferences: true,
    ),
    iOptions: IOSOptions(
      accessibility: KeychainAccessibility.first_unlock_this_device,
    ),
  );

  // Dio实例（用于token刷新）
  late final Dio _dio;
  
  // Token刷新状态管理
  bool _isRefreshing = false;
  final List<Completer<String?>> _refreshCompleters = [];
  
  final TalkerService _talkerService = GetIt.instance<TalkerService>();

  /// 初始化
  void initialize() {
    _dio = Dio(BaseOptions(
      connectTimeout: const Duration(seconds: 10),
      receiveTimeout: const Duration(seconds: 10),
      headers: {
        'User-Agent': userAgent,
        'Content-Type': 'application/x-www-form-urlencoded',
      },
    ));
  }

  /// 获取Access Token
  Future<String?> getAccessToken() async {
    try {
      return await _storage.read(key: _accessTokenKey);
    } catch (e) {
      _talkerService.logApiError('GET', '/token/access', 'Failed to read access token: $e');
      return null;
    }
  }

  /// 获取Refresh Token
  Future<String?> getRefreshToken() async {
    try {
      return await _storage.read(key: _refreshTokenKey);
    } catch (e) {
      _talkerService.logApiError('GET', '/token/refresh', 'Failed to read refresh token: $e');
      return null;
    }
  }

  /// 获取Token过期时间
  Future<DateTime?> getTokenExpiry() async {
    try {
      final expiryStr = await _storage.read(key: _tokenExpiryKey);
      if (expiryStr != null) {
        return DateTime.fromMillisecondsSinceEpoch(int.parse(expiryStr));
      }
    } catch (e) {
      _talkerService.logApiError('GET', '/token/expiry', 'Failed to read token expiry: $e');
    }
    return null;
  }

  /// 获取用户信息
  Future<Map<String, dynamic>?> getUserInfo() async {
    try {
      final userInfoStr = await _storage.read(key: _userInfoKey);
      if (userInfoStr != null) {
        return jsonDecode(userInfoStr) as Map<String, dynamic>;
      }
    } catch (e) {
      _talkerService.logApiError('GET', '/token/userinfo', 'Failed to read user info: $e');
    }
    return null;
  }

  /// 保存Token和相关信息
  Future<void> saveTokens({
    required String accessToken,
    required String refreshToken,
    DateTime? expiry,
    Map<String, dynamic>? userInfo,
  }) async {
    try {
      final operations = <Future<void>>[];
      
      if (accessToken.isNotEmpty) {
        operations.add(_storage.write(key: _accessTokenKey, value: accessToken));
      }
      
      if (refreshToken.isNotEmpty) {
        operations.add(_storage.write(key: _refreshTokenKey, value: refreshToken));
      }
      
      if (expiry != null) {
        operations.add(_storage.write(
          key: _tokenExpiryKey, 
          value: expiry.millisecondsSinceEpoch.toString(),
        ));
      }
      
      if (userInfo != null) {
        operations.add(_storage.write(
          key: _userInfoKey, 
          value: jsonEncode(userInfo),
        ));
      }
      
      await Future.wait(operations);
      _talkerService.logApiSuccess('POST', '/token/save', 200, const Duration(milliseconds: 1));
      
    } catch (e) {
      _talkerService.logApiError('POST', '/token/save', 'Failed to save tokens: $e');
      rethrow;
    }
  }

  /// 清除所有Token和相关信息
  Future<void> clearTokens() async {
    try {
      await Future.wait([
        _storage.delete(key: _accessTokenKey),
        _storage.delete(key: _refreshTokenKey),
        _storage.delete(key: _tokenExpiryKey),
        _storage.delete(key: _userInfoKey),
      ]);
      _talkerService.logApiSuccess('DELETE', '/token/clear', 200, const Duration(milliseconds: 1));
    } catch (e) {
      _talkerService.logApiError('DELETE', '/token/clear', 'Failed to clear tokens: $e');
      rethrow;
    }
  }

  /// 检查Token是否需要刷新（基于错误码）
  bool needsTokenRefresh(Map<String, dynamic> responseData) {
    final code = responseData['code'];
    return code == 40140123 || code == 40140124 || code == 40140125 || code == 40140126;
  }

  /// 检查Token是否即将过期（提前5分钟刷新）
  Future<bool> isTokenExpiringSoon() async {
    final expiry = await getTokenExpiry();
    if (expiry == null) return true;
    
    final now = DateTime.now();
    final warningTime = expiry.subtract(const Duration(minutes: 5));
    
    return now.isAfter(warningTime);
  }

  /// 检查是否有有效的Token
  Future<bool> hasValidTokens() async {
    final accessToken = await getAccessToken();
    final refreshToken = await getRefreshToken();
    return accessToken != null && refreshToken != null;
  }

  /// 刷新Access Token（带防重复调用保护）
  Future<String?> refreshAccessToken() async {
    // 如果正在刷新，等待当前刷新完成
    if (_isRefreshing) {
      final completer = Completer<String?>();
      _refreshCompleters.add(completer);
      return completer.future;
    }

    _isRefreshing = true;
    
    try {
      final refreshToken = await getRefreshToken();
      if (refreshToken == null) {
        _talkerService.logApiError('POST', '/token/refresh', 'No refresh token available');
        return null;
      }

      _talkerService.logApiStart('POST', '/token/refresh');

      final response = await _dio.post(
        refreshTokenUrl,
        data: {'refresh_token': refreshToken},
      );

      if (response.statusCode == 200) {
        final data = response.data as Map<String, dynamic>;
        
        if (data['state'] == 1 && data['code'] == 0) {
          final tokenData = data['data'] as Map<String, dynamic>;
          final newAccessToken = tokenData['access_token'] as String;
          final newRefreshToken = tokenData['refresh_token'] as String;
          
          // 计算过期时间（假设token有效期为1小时）
          final expiry = DateTime.now().add(const Duration(hours: 1));
          
          await saveTokens(
            accessToken: newAccessToken,
            refreshToken: newRefreshToken,
            expiry: expiry,
          );

          _talkerService.logApiSuccess('POST', '/token/refresh', 200, const Duration(milliseconds: 100));
          
          // 通知所有等待的请求
          for (final completer in _refreshCompleters) {
            if (!completer.isCompleted) {
              completer.complete(newAccessToken);
            }
          }
          _refreshCompleters.clear();
          
          return newAccessToken;
        } else {
          _talkerService.logApiError('POST', '/token/refresh', 'Token refresh failed: ${data['message']}');
        }
      } else {
        _talkerService.logApiError('POST', '/token/refresh', 'Token refresh HTTP error: ${response.statusCode}');
      }
      
    } catch (e) {
      _talkerService.logApiError('POST', '/token/refresh', 'Token refresh exception: $e');
    } finally {
      _isRefreshing = false;
      
      // 通知所有等待的请求失败
      for (final completer in _refreshCompleters) {
        if (!completer.isCompleted) {
          completer.complete(null);
        }
      }
      _refreshCompleters.clear();
    }
    
    return null;
  }

  /// 构建认证头
  Future<Map<String, String>> buildAuthHeaders([Map<String, String>? additionalHeaders]) async {
    final token = await getAccessToken();
    final headers = <String, String>{
      'User-Agent': userAgent,
      if (token != null) 'Authorization': 'Bearer $token',
    };
    
    if (additionalHeaders != null) {
      headers.addAll(additionalHeaders);
    }
    
    return headers;
  }

  /// 获取Token状态信息（用于调试）
  Future<Map<String, dynamic>> getTokenStatus() async {
    final accessToken = await getAccessToken();
    final refreshToken = await getRefreshToken();
    final expiry = await getTokenExpiry();
    final userInfo = await getUserInfo();
    
    return {
      'hasAccessToken': accessToken != null,
      'hasRefreshToken': refreshToken != null,
      'expiry': expiry?.toIso8601String(),
      'isExpiringSoon': await isTokenExpiringSoon(),
      'hasUserInfo': userInfo != null,
      'isRefreshing': _isRefreshing,
    };
  }
} 
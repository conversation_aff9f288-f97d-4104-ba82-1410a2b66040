import 'package:shared_preferences/shared_preferences.dart';

/// 视频预览设置服务
class VideoPreviewSettingsService {
  static const String _keyMinFrameCount = 'video_preview_min_frame_count';
  static const String _keyTargetFrameCount = 'video_preview_target_frame_count';
  static const String _keyMaxFrameCount = 'video_preview_max_frame_count';
  static const String _keyFrameQuality = 'video_preview_frame_quality';
  static const String _keyFrameSize = 'video_preview_frame_size';
  static const String _keyAnimationSpeed = 'video_preview_animation_speed';

  // 默认值
  static const int _defaultMinFrameCount = 6;
  static const int _defaultTargetFrameCount = 12;
  static const int _defaultMaxFrameCount = 20;
  static const int _defaultFrameQuality = 75;
  static const int _defaultFrameSize = 200;
  static const String _defaultAnimationSpeed = 'normal'; // slow, normal, fast

  /// 获取最小帧数
  static Future<int> getMinFrameCount() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getInt(_keyMinFrameCount) ?? _defaultMinFrameCount;
  }

  /// 设置最小帧数
  static Future<void> setMinFrameCount(int count) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setInt(_keyMinFrameCount, count);
  }

  /// 获取目标帧数
  static Future<int> getTargetFrameCount() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getInt(_keyTargetFrameCount) ?? _defaultTargetFrameCount;
  }

  /// 设置目标帧数
  static Future<void> setTargetFrameCount(int count) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setInt(_keyTargetFrameCount, count);
  }

  /// 获取最大帧数
  static Future<int> getMaxFrameCount() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getInt(_keyMaxFrameCount) ?? _defaultMaxFrameCount;
  }

  /// 设置最大帧数
  static Future<void> setMaxFrameCount(int count) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setInt(_keyMaxFrameCount, count);
  }

  /// 获取帧质量
  static Future<int> getFrameQuality() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getInt(_keyFrameQuality) ?? _defaultFrameQuality;
  }

  /// 设置帧质量
  static Future<void> setFrameQuality(int quality) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setInt(_keyFrameQuality, quality);
  }

  /// 获取帧尺寸
  static Future<int> getFrameSize() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getInt(_keyFrameSize) ?? _defaultFrameSize;
  }

  /// 设置帧尺寸
  static Future<void> setFrameSize(int size) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setInt(_keyFrameSize, size);
  }

  /// 获取动画速度
  static Future<String> getAnimationSpeed() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_keyAnimationSpeed) ?? _defaultAnimationSpeed;
  }

  /// 设置动画速度
  static Future<void> setAnimationSpeed(String speed) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_keyAnimationSpeed, speed);
  }

  /// 获取所有设置
  static Future<VideoPreviewSettings> getSettings() async {
    return VideoPreviewSettings(
      minFrameCount: await getMinFrameCount(),
      targetFrameCount: await getTargetFrameCount(),
      maxFrameCount: await getMaxFrameCount(),
      frameQuality: await getFrameQuality(),
      frameSize: await getFrameSize(),
      animationSpeed: await getAnimationSpeed(),
    );
  }

  /// 保存所有设置
  static Future<void> saveSettings(VideoPreviewSettings settings) async {
    await Future.wait([
      setMinFrameCount(settings.minFrameCount),
      setTargetFrameCount(settings.targetFrameCount),
      setMaxFrameCount(settings.maxFrameCount),
      setFrameQuality(settings.frameQuality),
      setFrameSize(settings.frameSize),
      setAnimationSpeed(settings.animationSpeed),
    ]);
  }

  /// 重置为默认设置
  static Future<void> resetToDefaults() async {
    final settings = VideoPreviewSettings(
      minFrameCount: _defaultMinFrameCount,
      targetFrameCount: _defaultTargetFrameCount,
      maxFrameCount: _defaultMaxFrameCount,
      frameQuality: _defaultFrameQuality,
      frameSize: _defaultFrameSize,
      animationSpeed: _defaultAnimationSpeed,
    );
    await saveSettings(settings);
  }
}

/// 视频预览设置数据类
class VideoPreviewSettings {
  final int minFrameCount;
  final int targetFrameCount;
  final int maxFrameCount;
  final int frameQuality;
  final int frameSize;
  final String animationSpeed;

  const VideoPreviewSettings({
    required this.minFrameCount,
    required this.targetFrameCount,
    required this.maxFrameCount,
    required this.frameQuality,
    required this.frameSize,
    required this.animationSpeed,
  });

  /// 获取动画间隔（毫秒）
  int get animationIntervalMs {
    switch (animationSpeed) {
      case 'slow':
        return 1000;
      case 'fast':
        return 300;
      case 'normal':
      default:
        return 600;
    }
  }

  /// 复制并修改设置
  VideoPreviewSettings copyWith({
    int? minFrameCount,
    int? targetFrameCount,
    int? maxFrameCount,
    int? frameQuality,
    int? frameSize,
    String? animationSpeed,
  }) {
    return VideoPreviewSettings(
      minFrameCount: minFrameCount ?? this.minFrameCount,
      targetFrameCount: targetFrameCount ?? this.targetFrameCount,
      maxFrameCount: maxFrameCount ?? this.maxFrameCount,
      frameQuality: frameQuality ?? this.frameQuality,
      frameSize: frameSize ?? this.frameSize,
      animationSpeed: animationSpeed ?? this.animationSpeed,
    );
  }

  @override
  String toString() {
    return 'VideoPreviewSettings(minFrameCount: $minFrameCount, targetFrameCount: $targetFrameCount, maxFrameCount: $maxFrameCount, frameQuality: $frameQuality, frameSize: $frameSize, animationSpeed: $animationSpeed)';
  }
}

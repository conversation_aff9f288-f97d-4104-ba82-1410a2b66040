import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';

class ImageCacheConfig {
  /// 构建带有错误处理的缓存网络图片组件
  static Widget buildCachedImage({
    required String imageUrl,
    BoxFit fit = BoxFit.cover,
    Widget? placeholder,
    Widget? errorWidget,
  }) {
    // 如果URL为空，直接返回错误组件
    if (imageUrl.isEmpty) {
      return errorWidget ?? _defaultErrorWidget();
    }

    return CachedNetworkImage(
      imageUrl: imageUrl,
      fit: fit,
      placeholder: (context, url) => placeholder ?? _defaultPlaceholder(),
      errorWidget: (context, url, error) {
        // 记录错误但不抛出异常
        debugPrint('图片加载失败: $url, 错误: $error');
        return errorWidget ?? _defaultErrorWidget();
      },
      // 添加缓存配置
      cacheKey: imageUrl,
      fadeInDuration: const Duration(milliseconds: 200),
      fadeOutDuration: const Duration(milliseconds: 200),
      // 设置更长的超时时间
      httpHeaders: const {
        'User-Agent': 'Flutter-115-App/1.0',
      },
    );
  }

  /// 默认加载中组件
  static Widget _defaultPlaceholder() {
    return Container(
      color: Colors.grey[100],
      child: const Center(
        child: CircularProgressIndicator(strokeWidth: 2),
      ),
    );
  }

  /// 默认错误组件
  static Widget _defaultErrorWidget() {
    return Container(
      color: Colors.grey[100],
      child: Icon(
        Icons.broken_image,
        size: 30,
        color: Colors.grey[400],
      ),
    );
  }

  /// 网格视图专用的图片组件
  static Widget buildGridImage({
    required String imageUrl,
    Widget? placeholder,
    Widget? errorWidget,
  }) {
    return buildCachedImage(
      imageUrl: imageUrl,
      fit: BoxFit.cover,
      placeholder: placeholder,
      errorWidget: errorWidget,
    );
  }

  /// 图片查看器专用的图片组件
  static Widget buildViewerImage({
    required String imageUrl,
    Widget? placeholder,
    Widget? errorWidget,
  }) {
    return buildCachedImage(
      imageUrl: imageUrl,
      fit: BoxFit.contain,
      placeholder: placeholder,
      errorWidget: errorWidget,
    );
  }
} 
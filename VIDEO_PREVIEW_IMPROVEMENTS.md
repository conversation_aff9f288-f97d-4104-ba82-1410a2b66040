# 视频预览功能改进

## 概述

本次改进大幅提升了视频预览功能，实现了智能的多帧提取和可配置的预览体验。

## 主要改进

### 1. 智能帧数计算
- **根据视频时长自动调整帧数**：
  - 短视频（≤30秒）：使用最少帧数（默认6帧）
  - 中等视频（≤5分钟）：使用目标帧数（默认12帧）
  - 长视频（≤30分钟）：使用目标帧数的1.5倍
  - 超长视频（>30分钟）：使用最大帧数（默认20帧）

### 2. 真实视频时长获取 ⭐ **重大改进**
- **直接使用API返回的真实时长**：从 `play_long` 字段获取准确的视频时长（单位：秒）
- **移除时长估算**：不再需要通过帧提取来估算时长，大大提高了性能和准确性
- **即时可用**：视频时长信息在文件列表加载时就已经可用，无需额外网络请求

### 3. 智能帧位置计算
- **避开开头和结尾**：跳过前后2秒或5%的内容
- **均匀分布**：在有效时间范围内均匀分布帧位置
- **动态调整**：根据视频时长和设置动态计算最佳帧位置

### 4. 可配置的预览设置
新增 `VideoPreviewSettingsService` 和设置页面，支持以下配置：

#### 帧数设置
- **最少帧数**：3-15帧（默认6帧）
- **目标帧数**：6-25帧（默认12帧）
- **最多帧数**：10-30帧（默认20帧）

#### 质量设置
- **图片质量**：30-100%（默认75%）
- **图片尺寸**：100-400px（默认200px）

#### 动画设置
- **播放速度**：慢（1000ms）、正常（600ms）、快（300ms）

### 5. 改进的用户界面
- **帧数显示**：显示当前帧数和总帧数
- **时长显示**：显示真实的视频时长（从API获取）
- **进度指示器**：显示当前帧在视频中的时间位置
- **动态播放速度**：根据帧数自动调整播放间隔

## 技术实现

### 核心组件

1. **VideoPreviewItem** - 改进的视频预览组件
   - 智能帧提取逻辑
   - 可配置的参数支持
   - 改进的UI显示

2. **VideoPreviewSettingsService** - 设置管理服务
   - 持久化存储设置
   - 默认值管理
   - 设置验证

3. **VideoPreviewSettingsPage** - 设置界面
   - 直观的滑块控制
   - 实时预览效果
   - 重置功能

### 关键算法

#### 真实视频时长获取
```dart
// 从 FileItem 获取真实的视频时长
if (widget.item.playLong != null && widget.item.playLong! > 0) {
  _videoDurationMs = widget.item.playLong! * 1000; // 转换为毫秒
  debugPrint('从API获取视频时长: ${widget.item.playLong}秒');
}
```

#### 帧位置计算
```dart
void _calculateFramePositions() {
  final startOffset = min(2000, videoDuration * 0.05);
  final endOffset = min(2000, videoDuration * 0.05);
  final availableDuration = videoDuration - startOffset - endOffset;
  
  final interval = availableDuration / (frameCount - 1);
  
  for (int i = 0; i < frameCount; i++) {
    final timeMs = (startOffset + i * interval).round();
    frameTimePositions.add(timeMs);
  }
}
```

## 使用方法

### 1. 基本使用
视频预览功能会自动应用到所有视频文件，无需额外配置。

### 2. 调整设置
1. 进入应用设置页面
2. 找到"视频预览设置"选项
3. 调整帧数、质量和动画速度
4. 保存设置

### 3. 设置说明
- **帧数设置**：控制预览的丰富程度，帧数越多预览越详细
- **质量设置**：平衡清晰度和加载速度
- **动画设置**：控制帧切换的快慢

## 性能优化

### 1. 智能提取策略
- 根据视频时长动态调整帧数
- 提取过程中的错误处理和恢复
- 提前终止机制（达到最少帧数后可提前结束）

### 2. 网络优化
- 请求间隔控制（80ms延迟）
- 批量处理失败重试
- 渐进式加载

### 3. 内存管理
- 合理的图片尺寸设置
- 及时清理资源
- 避免内存泄漏

## 兼容性

- 支持所有现有的视频格式
- 向后兼容原有的预览功能
- 设置更改实时生效

## 未来改进方向

1. **更精确的时长获取**：集成视频元数据解析库
2. **缓存机制**：缓存已提取的帧，避免重复提取
3. **预加载策略**：预先提取可见区域的视频帧
4. **自适应质量**：根据网络状况自动调整质量
5. **用户偏好学习**：根据用户行为自动优化设置

## 总结

本次改进显著提升了视频预览的用户体验：
- **更多帧数**：从固定5帧提升到最多30帧
- **智能适配**：根据视频时长自动调整策略
- **可配置性**：用户可根据需求自定义设置
- **更好的UI**：显示更多有用信息
- **性能优化**：平衡预览质量和加载速度

这些改进让视频预览功能更加实用和用户友好，为用户提供了更丰富的视频内容预览体验。

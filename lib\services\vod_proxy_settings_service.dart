import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// VodProxy设置服务
/// 管理VodProxyService的启用/禁用状态
class VodProxySettingsService extends ChangeNotifier {
  static const String _enabledKey = 'vod_proxy_enabled';
  static const String _portKey = 'vod_proxy_port';
  static const String _threadCountKey = 'vod_proxy_thread_count';
  static const String _chunkSizeKey = 'vod_proxy_chunk_size';

  bool _isEnabled = false;
  int _port = 37150; // 默认端口
  int _threadCount = 2; // 默认线程数
  int _chunkSize = 10240; // 默认分片大小 (KB)
  bool _isInitialized = false;
  
  /// 获取VodProxy是否启用
  bool get isEnabled => _isEnabled;

  /// 获取VodProxy端口
  int get port => _port;

  /// 获取线程数
  int get threadCount => _threadCount;

  /// 获取分片大小 (KB)
  int get chunkSize => _chunkSize;

  /// 获取是否已初始化
  bool get isInitialized => _isInitialized;
  
  /// 单例模式
  static final VodProxySettingsService _instance = VodProxySettingsService._internal();
  factory VodProxySettingsService() => _instance;
  VodProxySettingsService._internal();
  
  /// 初始化服务
  Future<void> initialize() async {
    if (_isInitialized) return;
    
    try {
      await _loadSettings();
      _isInitialized = true;
    } catch (e) {
      // 如果加载失败，使用默认值
      _isEnabled = false;
      _port = 37150;
      _threadCount = 2;
      _chunkSize = 10240;
      _isInitialized = true;
    }
  }
  
  /// 从SharedPreferences加载设置
  Future<void> _loadSettings() async {
    final prefs = await SharedPreferences.getInstance();
    _isEnabled = prefs.getBool(_enabledKey) ?? false;
    _port = prefs.getInt(_portKey) ?? 37150;
    _threadCount = prefs.getInt(_threadCountKey) ?? 2;
    _chunkSize = prefs.getInt(_chunkSizeKey) ?? 10240;
  }
  
  /// 设置VodProxy启用状态
  Future<void> setEnabled(bool enabled) async {
    if (_isEnabled != enabled) {
      _isEnabled = enabled;
      notifyListeners();
      
      try {
        final prefs = await SharedPreferences.getInstance();
        await prefs.setBool(_enabledKey, enabled);
      } catch (e) {
        // 保存失败不影响功能，但可以记录日志
        debugPrint('Failed to save VodProxy enabled setting: $e');
      }
    }
  }
  
  /// 设置VodProxy端口
  Future<void> setPort(int port) async {
    if (_port != port && port > 0 && port <= 65535) {
      _port = port;
      notifyListeners();
      
      try {
        final prefs = await SharedPreferences.getInstance();
        await prefs.setInt(_portKey, port);
      } catch (e) {
        // 保存失败不影响功能，但可以记录日志
        debugPrint('Failed to save VodProxy port setting: $e');
      }
    }
  }

  /// 设置线程数
  Future<void> setThreadCount(int threadCount) async {
    if (_threadCount != threadCount && threadCount > 0 && threadCount <= 32) {
      _threadCount = threadCount;
      notifyListeners();

      try {
        final prefs = await SharedPreferences.getInstance();
        await prefs.setInt(_threadCountKey, threadCount);
      } catch (e) {
        // 保存失败不影响功能，但可以记录日志
        debugPrint('Failed to save VodProxy thread count setting: $e');
      }
    }
  }

  /// 设置分片大小 (KB)
  Future<void> setChunkSize(int chunkSize) async {
    if (_chunkSize != chunkSize && chunkSize >= 1024 && chunkSize <= 102400) {
      _chunkSize = chunkSize;
      notifyListeners();

      try {
        final prefs = await SharedPreferences.getInstance();
        await prefs.setInt(_chunkSizeKey, chunkSize);
      } catch (e) {
        // 保存失败不影响功能，但可以记录日志
        debugPrint('Failed to save VodProxy chunk size setting: $e');
      }
    }
  }

  /// 重置为默认设置
  Future<void> resetToDefaults() async {
    _isEnabled = false;
    _port = 37150;
    _threadCount = 2;
    _chunkSize = 10240;
    notifyListeners();

    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_enabledKey, false);
      await prefs.setInt(_portKey, 37150);
      await prefs.setInt(_threadCountKey, 2);
      await prefs.setInt(_chunkSizeKey, 10240);
    } catch (e) {
      debugPrint('Failed to reset VodProxy settings: $e');
    }
  }
  
  /// 获取设置摘要信息
  String get settingsSummary {
    if (!_isEnabled) {
      return '已禁用';
    }
    return '已启用 (端口: $_port, 线程: $_threadCount, 分片: ${_chunkSize}KB)';
  }

  /// 重置服务状态（仅用于测试）
  void resetForTesting() {
    _isEnabled = false;
    _port = 37150;
    _threadCount = 2;
    _chunkSize = 10240;
    _isInitialized = false;
  }
}

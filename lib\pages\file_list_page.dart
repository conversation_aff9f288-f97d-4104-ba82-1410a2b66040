import 'package:flutter/material.dart';
import '../models/file_item.dart';
import '../services/api_service.dart';

import '../services/file_action_service.dart';
import '../widgets/breadcrumb_navigation.dart';
import '../widgets/sort_dialog.dart';
import '../widgets/error_display.dart';
import '../widgets/file_list_item.dart';
import '../widgets/video_preview_manager.dart';
import '../utils/file_type_utils.dart';
import 'settings_page.dart';

/// 视图模式枚举
enum ViewMode {
  list,         // 列表视图
  grid,         // 网格视图
  videoPreview, // 视频预览模式
}

class FileListPage extends StatefulWidget {
  final String? cid;
  final String? title;

  const FileListPage({
    super.key,
    this.cid,
    this.title,
  });

  @override
  State<FileListPage> createState() => _FileListPageState();
}

class _FileListPageState extends State<FileListPage> {
  final ApiService _apiService = ApiService();
  final ScrollController _scrollController = ScrollController();
  
  List<FileItem> _files = [];
  List<PathItem> _currentPath = [];
  bool _isLoading = false;
  bool _hasMore = true;
  String? _error;
  bool _hasLoadMoreError = false;
  String? _loadMoreError;
  int _offset = 0;
  static const int _limit = 50;
  
  // 排序配置
  SortConfig _sortConfig = const SortConfig(
    option: SortOption.name,
    direction: SortDirection.asc,
  );
  
  // 视图模式
  ViewMode _viewMode = ViewMode.list;

  // 视频预览相关状态
  bool _isVideoPreviewMode = false;
  List<FileItem> _videoFiles = [];
  Map<String, String> _videoPlayUrls = {};

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);
    _loadFiles();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels >= 
        _scrollController.position.maxScrollExtent - 200) {
      if (!_isLoading && _hasMore && mounted) {
        _loadMoreFiles();
      }
    }
  }

  Future<void> _loadFiles() async {
    if (_isLoading) return;

    setState(() {
      _isLoading = true;
      _error = null;
      _offset = 0;
      _files.clear();
      _hasMore = true; // 重置hasMore状态
      _hasLoadMoreError = false; // 重置加载更多错误状态
      _loadMoreError = null;
    });

    try {
      await _fetchFiles();
    } catch (e) {
      setState(() {
        _error = '加载失败: $e';
        _hasMore = false; // 加载失败时设置为false
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _loadMoreFiles() async {
    if (_isLoading || !_hasMore || !mounted) return;

    setState(() {
      _isLoading = true;
      _hasLoadMoreError = false;
      _loadMoreError = null;
    });

    try {
      await _fetchFiles();
    } catch (e) {
      if (mounted) {
        setState(() {
          _hasLoadMoreError = true;
          _loadMoreError = e.toString();
        });
        
        // 显示简短的错误提示
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('加载更多失败: ${e.toString().length > 50 ? '${e.toString().substring(0, 50)}...' : e.toString()}'),
            action: SnackBarAction(
              label: '重试',
              onPressed: () {
                if (mounted && !_isLoading) {
                  _loadMoreFiles();
                }
              },
            ),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _fetchFiles() async {
    final cid = widget.cid ?? '0';
    
    // 使用API调用，拦截器会自动处理重试，首次加载使用缓存
    final response = await _apiService.get(
      '/open/ufile/files', 
      queryParams: {
        'cid': cid,
        'offset': _offset.toString(),
        'limit': _limit.toString(),
        'stdir': '1',
        'show_dir': '1',
        'o': _sortConfig.apiSortValue,
        'asc': _sortConfig.apiDirectionValue,
        'custom_order': '1',
      },
              // 缓存由 dio_cache_interceptor 自动处理
    );

    if (response.statusCode == 200) {
      final data = response.data as Map<String, dynamic>;
      
      // 检查115 API的错误码
      if (data['code'] != null && data['code'] != 0) {
        throw Exception('API错误: ${data['code']} - ${data['message'] ?? '未知错误'}');
      }
      
      if (data['data'] != null) {
        final List<dynamic> filesJson = data['data'] as List<dynamic>? ?? [];
        
        final List<FileItem> newFiles = <FileItem>[];
        List<PathItem> pathItems = [];
        
        // 处理path信息 - 首先尝试从响应根级别获取
        if (_offset == 0) {
          // 尝试从响应的根级别获取path信息
          if (data['path'] is List) {
            pathItems = (data['path'] as List)
                .map((pathJson) => PathItem.fromJson(pathJson as Map<String, dynamic>))
                .where((pathItem) => _isValidPathItem(pathItem))
                .toList();
          }
          // 如果根级别没有，尝试从第一个文件项获取
          else if (filesJson.isNotEmpty) {
            final firstItem = filesJson.first as Map<String, dynamic>;
            if (firstItem['path'] is List) {
              pathItems = (firstItem['path'] as List)
                  .map((pathJson) => PathItem.fromJson(pathJson as Map<String, dynamic>))
                  .where((pathItem) => _isValidPathItem(pathItem))
                  .toList();
            }
          }
          // 如果都没有path信息，基于当前导航状态构建
          else if (widget.cid != null && widget.cid != '0' && widget.title != null) {
            // 基于传入的cid和title构建path
            pathItems = _buildPathFromNavigation();
          }
        }
        
        for (int i = 0; i < filesJson.length; i++) {
          try {
            final json = filesJson[i] as Map<String, dynamic>;
            final fileItem = FileItem.fromJson(json);
            newFiles.add(fileItem);
          } catch (e) {
            // 跳过有问题的项目，继续处理其他项目
            continue;
          }
        }

        setState(() {
          if (_offset == 0) {
            _files = newFiles;
            _currentPath = pathItems;
            // 自动检测是否应该使用网格视图
            if (_shouldUseGridView()) {
              _viewMode = ViewMode.grid;
            }
          } else {
            _files.addAll(newFiles);
          }
          _offset += newFiles.length;
          // 改进_hasMore判断逻辑：只有当返回的文件数等于限制数量时才认为可能有更多文件
          // 但如果返回0个文件，明确设置为false
          if (newFiles.isEmpty) {
            _hasMore = false;
          } else {
            _hasMore = newFiles.length == _limit;
          }
        });
      } else {
        throw Exception(data['message'] ?? '未知错误');
      }
    } else {
      throw Exception('HTTP ${response.statusCode}');
    }
  }

  void _onFileItemTap(FileItem item) {
    FileActionService.handleFileItemTap(context, item, _files);
  }



  /// 处理排序更改
  Future<void> _onSortChanged() async {
    final newSort = await showSortDialog(context, _sortConfig);
    if (newSort != null && newSort != _sortConfig) {
      setState(() {
        _sortConfig = newSort;
      });
      _loadFiles(); // 重新加载数据
    }
  }

  /// 切换视图模式
  void _toggleViewMode() {
    setState(() {
      // 如果当前在视频预览模式，先退出视频预览模式
      if (_viewMode == ViewMode.videoPreview) {
        _isVideoPreviewMode = false;
        _videoFiles.clear();
        _videoPlayUrls.clear();
      }

      // 在列表和网格视图之间切换
      if (_viewMode == ViewMode.list || _viewMode == ViewMode.videoPreview) {
        _viewMode = ViewMode.grid;
      } else {
        _viewMode = ViewMode.list;
      }
    });
  }

  /// 检查是否应该自动使用网格视图
  bool _shouldUseGridView() {
    return FileActionService.shouldUseGridView(_files);
  }

  /// 检查当前页面是否有视频文件
  bool _hasVideoFiles() {
    return _files.any((file) => file.isVideo || FileTypeUtils.isVideoFile(file));
  }

  /// 获取当前页面的视频文件列表
  List<FileItem> _getVideoFiles() {
    return _files.where((file) => file.isVideo || FileTypeUtils.isVideoFile(file)).toList();
  }

  /// 切换视频预览模式
  void _toggleVideoPreviewMode() {
    setState(() {
      if (_viewMode == ViewMode.videoPreview) {
        // 退出视频预览模式，回到之前的视图模式
        _viewMode = _shouldUseGridView() ? ViewMode.grid : ViewMode.list;
        _isVideoPreviewMode = false;
        _videoFiles.clear();
        _videoPlayUrls.clear();
      } else {
        // 进入视频预览模式
        _viewMode = ViewMode.videoPreview;
        _isVideoPreviewMode = true;
        _videoFiles = _getVideoFiles();
        _loadVideoPreviewUrls();
      }
    });
  }

  /// 加载视频预览播放地址
  Future<void> _loadVideoPreviewUrls() async {
    if (_videoFiles.isEmpty) return;

    try {
      final pickCodes = _videoFiles
          .where((file) => file.pickCode != null && file.pickCode!.isNotEmpty)
          .map((file) => file.pickCode!)
          .toList();

      if (pickCodes.isNotEmpty) {
        final playUrls = await _apiService.getBatchPlayUrls(pickCodes);
        setState(() {
          _videoPlayUrls = playUrls;
        });
      }
    } catch (e) {
      // 静默处理错误，不影响主要功能
      debugPrint('加载视频预览地址失败: $e');
    }
  }



  /// 处理面包屑导航
  void _onBreadcrumbNavigate(String cid, String name) {
    FileActionService.handleBreadcrumbNavigate(
      context, 
      cid, 
      name, 
      widget.cid, 
      _currentPath,
    );
  }

  /// 基于导航状态构建path信息（兜底方案）
  List<PathItem> _buildPathFromNavigation() {
    // 这是一个简化的实现，只包含当前目录
    // 在实际应用中，您可能需要存储完整的导航历史
    if (widget.cid != null && widget.cid != '0' && widget.title != null) {
      return [
        PathItem(id: widget.cid!, name: widget.title!),
      ];
    }
    return [];
  }

  /// 获取要显示的路径信息
  List<PathItem> _getDisplayPath() {
    // 如果有API返回的path信息，优先使用
    if (_currentPath.isNotEmpty) {
      return _currentPath;
    }
    
    // 如果没有API path信息，使用兜底方案
    // 根目录返回空列表，子目录返回当前目录信息
    if (widget.cid == null || widget.cid == '0') {
      return []; // 根目录
    } else {
      // 子目录，至少显示当前目录
      return widget.title != null 
          ? [PathItem(id: widget.cid!, name: widget.title!)]
          : [];
    }
  }

  /// 检查路径项是否应该在面包屑中显示
  bool _isValidPathItem(PathItem pathItem) {
    // 过滤掉空名称或无效ID
    if (pathItem.name.isEmpty || pathItem.id.isEmpty) {
      return false;
    }
    
    // 过滤掉一些通用的、不具体的路径名称
    final lowerName = pathItem.name.toLowerCase();
    const invalidNames = [
      '文件',
      'files', 
      'file',
      '全部文件',
      'all files',
      '我的文件',
      'my files',
    ];
    
    return !invalidNames.contains(lowerName);
  }



  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.title ?? '我的文件'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        actions: [
          // 视频预览按钮 - 只在有视频文件时显示
          if (_hasVideoFiles())
            IconButton(
              icon: Icon(_viewMode == ViewMode.videoPreview ? Icons.video_library : Icons.play_circle_outline),
              onPressed: _toggleVideoPreviewMode,
              tooltip: _viewMode == ViewMode.videoPreview ? '退出视频预览' : '视频预览',
            ),
          IconButton(
            icon: Icon(_viewMode == ViewMode.list ? Icons.grid_view : Icons.list),
            onPressed: _toggleViewMode,
            tooltip: _viewMode == ViewMode.list ? '网格视图' : '列表视图',
          ),
          IconButton(
            icon: const Icon(Icons.sort),
            onPressed: _onSortChanged,
            tooltip: '排序: ${_sortConfig.displayName}',
          ),
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: () => Navigator.of(context).push(
              MaterialPageRoute(
                builder: (context) => const SettingsPage(),
              ),
            ),
            tooltip: '设置',
          ),
        ],
      ),
      body: Column(
        children: [
          // 面包屑导航 - 始终显示
          Container(
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surface,
              border: Border(
                bottom: BorderSide(
                  color: Theme.of(context).dividerColor,
                  width: 0.5,
                ),
              ),
            ),
            child: BreadcrumbNavigation(
              path: _getDisplayPath(),
              onNavigate: _onBreadcrumbNavigate,
            ),
          ),
          // 文件列表
          Expanded(child: _buildBody()),
        ],
      ),
    );
  }

  Widget _buildBody() {
    if (_error != null && _files.isEmpty) {
      return LoadingErrorWidget(
        error: _error,
        onRetry: _loadFiles,
      );
    }

    if (_isLoading && _files.isEmpty) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (_files.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.folder_open,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              '此文件夹为空',
              style: TextStyle(
                color: Colors.grey[600],
                fontSize: 16,
              ),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _loadFiles,
      child: _buildCurrentView(),
    );
  }

  /// 构建当前视图
  Widget _buildCurrentView() {
    switch (_viewMode) {
      case ViewMode.list:
        return _buildListView();
      case ViewMode.grid:
        return _buildGridView();
      case ViewMode.videoPreview:
        return _buildVideoPreviewView();
    }
  }

  /// 构建视频预览视图
  Widget _buildVideoPreviewView() {
    if (_videoFiles.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.video_library_outlined,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              '此文件夹没有视频文件',
              style: TextStyle(
                color: Colors.grey[600],
                fontSize: 16,
              ),
            ),
          ],
        ),
      );
    }

    return VideoPreviewList(
      videoFiles: _videoFiles,
      playUrls: _videoPlayUrls,
      onVideoTap: _onFileItemTap,
      scrollController: _scrollController,
    );
  }

  /// 构建列表视图
  Widget _buildListView() {
    return ListView.builder(
      controller: _scrollController,
      itemCount: _files.length + (_hasMore ? 1 : 0),
      itemBuilder: (context, index) {
        if (index < _files.length) {
          return FileListTile(
            item: _files[index],
            onTap: () => _onFileItemTap(_files[index]),
          );
        } else if (_hasMore) {
          // 只有在确实还有更多文件时才显示加载更多指示器
          return ListLoadingIndicator(
            isLoading: _isLoading,
            hasError: _hasLoadMoreError,
            error: _loadMoreError,
            onRetry: () {
              if (mounted && !_isLoading && _hasMore) {
                _loadMoreFiles();
              }
            },
          );
        } else {
          // 没有更多文件时显示结束提示
          return Container(
            padding: const EdgeInsets.all(16),
            child: Text(
              '已加载全部文件',
              style: TextStyle(
                color: Colors.grey[600],
                fontSize: 14,
              ),
              textAlign: TextAlign.center,
            ),
          );
        }
      },
    );
  }

  /// 构建网格视图
  Widget _buildGridView() {
    return GridView.builder(
      controller: _scrollController,
      padding: const EdgeInsets.all(8),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 3,
        crossAxisSpacing: 8,
        mainAxisSpacing: 8,
        childAspectRatio: 1,
      ),
      itemCount: _files.length + (_hasMore ? 1 : 0),
      itemBuilder: (context, index) {
        if (index < _files.length) {
          return FileGridItem(
            item: _files[index],
            onTap: () => _onFileItemTap(_files[index]),
          );
        } else if (_hasMore) {
          // 只有在确实还有更多文件时才显示加载更多指示器
          return Container(
            alignment: Alignment.center,
            child: ListLoadingIndicator(
              isLoading: _isLoading,
              hasError: _hasLoadMoreError,
              error: _loadMoreError,
              onRetry: () {
                if (mounted && !_isLoading && _hasMore) {
                  _loadMoreFiles();
                }
              },
            ),
          );
        } else {
          // 没有更多文件时显示结束提示
          return Container(
            alignment: Alignment.center,
            padding: const EdgeInsets.all(16),
            child: Text(
              '已加载全部文件',
              style: TextStyle(
                color: Colors.grey[600],
                fontSize: 14,
              ),
              textAlign: TextAlign.center,
            ),
          );
        }
      },
    );
  }


} 
import 'package:flutter/material.dart';
import '../services/video_frame_extraction_manager.dart';
import '../widgets/video_frame_demo_widget.dart';
import '../pages/video_frame_test_page.dart';

/// 视频帧提取功能使用示例
/// 展示如何在实际应用中集成和使用锁机制和限流功能
class VideoFrameUsageExample extends StatefulWidget {
  const VideoFrameUsageExample({super.key});

  @override
  State<VideoFrameUsageExample> createState() => _VideoFrameUsageExampleState();
}

class _VideoFrameUsageExampleState extends State<VideoFrameUsageExample> {
  final VideoFrameExtractionManager _frameManager = VideoFrameExtractionManager();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('视频帧提取功能示例'),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 功能介绍卡片
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '视频帧提取锁机制和限流系统',
                      style: Theme.of(context).textTheme.titleLarge,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      '这个系统实现了以下核心功能：',
                      style: Theme.of(context).textTheme.bodyMedium,
                    ),
                    const SizedBox(height: 8),
                    _buildFeatureList(),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // 演示组件
            const VideoFrameDemoWidget(),
            
            const SizedBox(height: 16),
            
            // 导航按钮
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '更多功能',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const SizedBox(height: 16),
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton.icon(
                        onPressed: () {
                          Navigator.of(context).push(
                            MaterialPageRoute(
                              builder: (context) => const VideoFrameTestPage(),
                            ),
                          );
                        },
                        icon: const Icon(Icons.science),
                        label: const Text('打开完整测试页面'),
                      ),
                    ),
                    const SizedBox(height: 8),
                    SizedBox(
                      width: double.infinity,
                      child: OutlinedButton.icon(
                        onPressed: _showUsageDialog,
                        icon: const Icon(Icons.code),
                        label: const Text('查看代码示例'),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // 技术细节
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '技术实现',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const SizedBox(height: 8),
                    _buildTechnicalDetails(),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFeatureList() {
    final features = [
      '🔒 全局锁机制：确保同一时间只能有一个视频在提取帧',
      '⏱️ 限流控制：每秒最多提取2帧，避免过度占用资源',
      '📋 自动排队：多个提取请求会自动排队等待',
      '📊 实时监控：提供详细的状态信息和统计数据',
      '🔄 错误处理：自动处理提取失败的情况',
      '📈 性能优化：减少内存压力和网络请求频率',
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: features.map((feature) => Padding(
        padding: const EdgeInsets.symmetric(vertical: 2),
        child: Text(
          feature,
          style: Theme.of(context).textTheme.bodySmall,
        ),
      )).toList(),
    );
  }

  Widget _buildTechnicalDetails() {
    final details = [
      '• 使用 Mutex 实现全局锁机制',
      '• 基于时间间隔的限流控制（500ms间隔）',
      '• 单例模式确保全局状态一致性',
      '• 支持进度回调和实时状态监控',
      '• 集成到现有的 VideoPreviewItem 组件',
      '• 兼容 get_thumbnail_video 插件',
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: details.map((detail) => Padding(
        padding: const EdgeInsets.symmetric(vertical: 2),
        child: Text(
          detail,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            fontFamily: 'monospace',
          ),
        ),
      )).toList(),
    );
  }

  void _showUsageDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('代码使用示例'),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                '基本使用：',
                style: Theme.of(context).textTheme.titleSmall,
              ),
              const SizedBox(height: 8),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(4),
                ),
                child: const Text(
                  '''final frameManager = VideoFrameExtractionManager();

// 提取单个帧
final frameData = await frameManager.extractFrame(
  videoUrl: 'https://example.com/video.mp4',
  timeMs: 5000,
  maxWidth: 200,
  maxHeight: 200,
  quality: 75,
);

// 批量提取帧
final frames = await frameManager.extractFrames(
  videoUrl: 'https://example.com/video.mp4',
  timePositions: [1000, 3000, 5000],
  maxWidth: 200,
  maxHeight: 200,
  quality: 75,
  onProgress: (current, total, timeMs) {
    print('进度: \$current/\$total');
  },
);''',
                  style: TextStyle(
                    fontFamily: 'monospace',
                    fontSize: 12,
                  ),
                ),
              ),
              const SizedBox(height: 16),
              Text(
                '状态监控：',
                style: Theme.of(context).textTheme.titleSmall,
              ),
              const SizedBox(height: 8),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(4),
                ),
                child: const Text(
                  '''// 获取统计信息
final stats = frameManager.getStatistics();
print('成功率: \${stats['successRate']}');

// 检查提取状态
if (frameManager.isExtracting) {
  print('正在提取: \${frameManager.currentExtractionVideo}');
}''',
                  style: TextStyle(
                    fontFamily: 'monospace',
                    fontSize: 12,
                  ),
                ),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('关闭'),
          ),
        ],
      ),
    );
  }
}

/// 简化的使用示例函数
class VideoFrameExtractionExamples {
  static final VideoFrameExtractionManager _frameManager = VideoFrameExtractionManager();

  /// 示例1：提取单个帧
  static Future<void> extractSingleFrame() async {
    final frameData = await _frameManager.extractFrame(
      videoUrl: 'https://example.com/video.mp4',
      timeMs: 5000, // 5秒位置
      maxWidth: 200,
      maxHeight: 200,
      quality: 75,
      onProgress: (videoUrl, timeMs) {
        print('正在提取帧：$videoUrl 时间点：${timeMs}ms');
      },
    );

    if (frameData != null) {
      print('帧提取成功：${frameData.length} bytes');
      // 在这里使用帧数据，例如显示在UI中
    } else {
      print('帧提取失败');
    }
  }

  /// 示例2：批量提取帧
  static Future<void> extractMultipleFrames() async {
    final timePositions = [1000, 3000, 5000, 7000, 9000];
    
    final frames = await _frameManager.extractFrames(
      videoUrl: 'https://example.com/video.mp4',
      timePositions: timePositions,
      maxWidth: 200,
      maxHeight: 200,
      quality: 75,
      onProgress: (current, total, timeMs) {
        print('批量提取进度：$current/$total 帧，当前时间点：${timeMs}ms');
      },
      onFrameExtracted: (index, frameData) {
        if (frameData != null) {
          print('第${index + 1}帧提取成功：${frameData.length} bytes');
          // 可以在这里实时处理每一帧
        } else {
          print('第${index + 1}帧提取失败');
        }
      },
    );

    print('批量提取完成：成功${frames.length}/${timePositions.length}帧');
  }

  /// 示例3：监控提取状态
  static void monitorExtractionStatus() {
    final stats = _frameManager.getStatistics();
    
    print('=== 视频帧提取统计 ===');
    print('总提取次数：${stats['totalExtractions']}');
    print('成功次数：${stats['successfulExtractions']}');
    print('失败次数：${stats['failedExtractions']}');
    print('成功率：${stats['successRate']}');
    print('是否正在提取：${stats['isCurrentlyExtracting']}');
    print('当前提取视频：${stats['currentVideo'] ?? '无'}');
    print('限流间隔：${stats['frameIntervalMs']}ms');
  }

  /// 示例4：并发提取测试（验证锁机制）
  static Future<void> testConcurrentExtraction() async {
    final videoUrls = [
      'https://example.com/video1.mp4',
      'https://example.com/video2.mp4',
      'https://example.com/video3.mp4',
    ];

    print('开始并发提取测试...');
    
    // 同时启动多个提取任务
    final futures = videoUrls.map((url) => _frameManager.extractFrame(
      videoUrl: url,
      timeMs: 5000,
      maxWidth: 200,
      maxHeight: 200,
      quality: 75,
    ));

    final results = await Future.wait(futures);
    
    print('并发提取完成：');
    for (int i = 0; i < results.length; i++) {
      final result = results[i];
      print('视频${i + 1}：${result != null ? '成功' : '失败'}');
    }
  }
}

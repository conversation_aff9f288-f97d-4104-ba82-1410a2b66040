import 'dart:async';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';

import 'token_manager.dart';


/// Token验证结果
class TokenValidationResult {
  final bool isValid;
  final TokenValidationStatus status;
  final String? errorMessage;
  final int? errorCode;
  final bool wasRefreshed;

  const TokenValidationResult({
    required this.isValid,
    required this.status,
    this.errorMessage,
    this.errorCode,
    this.wasRefreshed = false,
  });

  /// 创建成功结果
  factory TokenValidationResult.success({bool wasRefreshed = false}) {
    return TokenValidationResult(
      isValid: true,
      status: TokenValidationStatus.valid,
      wasRefreshed: wasRefreshed,
    );
  }

  /// 创建需要重新登录的结果
  factory TokenValidationResult.needsRelogin(String reason, {int? errorCode}) {
    return TokenValidationResult(
      isValid: false,
      status: TokenValidationStatus.needsRelogin,
      errorMessage: reason,
      errorCode: errorCode,
    );
  }

  /// 创建网络错误结果
  factory TokenValidationResult.networkError(String error) {
    return TokenValidationResult(
      isValid: false,
      status: TokenValidationStatus.networkError,
      errorMessage: error,
    );
  }

  /// 创建无Token结果
  factory TokenValidationResult.noToken() {
    return const TokenValidationResult(
      isValid: false,
      status: TokenValidationStatus.noToken,
      errorMessage: '未找到访问令牌',
    );
  }

  /// 创建刷新失败结果
  factory TokenValidationResult.refreshFailed(String reason) {
    return TokenValidationResult(
      isValid: false,
      status: TokenValidationStatus.refreshFailed,
      errorMessage: reason,
    );
  }

  /// 创建未知错误结果
  factory TokenValidationResult.unknownError(String error) {
    return TokenValidationResult(
      isValid: false,
      status: TokenValidationStatus.unknownError,
      errorMessage: error,
    );
  }
}

/// Token验证状态枚举
enum TokenValidationStatus {
  valid,           // Token有效
  noToken,         // 没有Token
  needsRelogin,    // 需要重新登录（refresh token无效/过期）
  refreshFailed,   // 刷新失败
  networkError,    // 网络错误
  unknownError,    // 未知错误
}

/// 强大的ApiService - 基于Dio和依赖注入的现代实现
/// 所有HTTP日志记录现在由TalkerDioLogger自动处理
class ApiService {
  static final ApiService _instance = ApiService._internal();
  factory ApiService() => _instance;
  ApiService._internal();

  // 依赖注入的服务
  late final Dio _dio;
  late final TokenManager _tokenManager;

  /// 初始化服务（必须在使用前调用）
  void initialize() {
    _dio = GetIt.instance<Dio>();
    _tokenManager = GetIt.instance<TokenManager>();
  }

  /// GET请求（使用dio_cache_interceptor自动缓存）
  /// 日志记录现在由TalkerDioLogger自动处理
  Future<Response> get(String endpoint, {
    Map<String, dynamic>? queryParams,
    BuildContext? context,
    CancelToken? cancelToken,
  }) async {
    // TalkerDioLogger会自动记录请求和响应
    return await _dio.get(
      endpoint,
      queryParameters: queryParams,
      cancelToken: cancelToken,
    );
  }

  /// POST请求（JSON数据）
  /// 日志记录现在由TalkerDioLogger自动处理
  Future<Response> post(String endpoint, {
    Map<String, dynamic>? data,
    Map<String, dynamic>? queryParams,
    Map<String, String>? headers,
    BuildContext? context,
    CancelToken? cancelToken,
  }) async {
    // TalkerDioLogger会自动记录请求和响应
    return await _dio.post(
      endpoint,
      data: data,
      queryParameters: queryParams,
      options: Options(
        headers: headers,
        contentType: Headers.jsonContentType,
      ),
      cancelToken: cancelToken,
    );
  }

  /// POST请求（表单数据）
  /// 日志记录现在由TalkerDioLogger自动处理
  Future<Response> postFormData(String endpoint, {
    Map<String, dynamic>? formData,
    Map<String, dynamic>? queryParams,
    Map<String, String>? headers,
    BuildContext? context,
    CancelToken? cancelToken,
  }) async {
    // TalkerDioLogger会自动记录请求和响应
    return await _dio.post(
      endpoint,
      data: FormData.fromMap(formData ?? {}),
      queryParameters: queryParams,
      options: Options(
        headers: headers,
        contentType: Headers.formUrlEncodedContentType,
      ),
      cancelToken: cancelToken,
    );
  }

  /// 检查Token是否有效
  Future<TokenValidationResult> isTokenValid() async {
    final hasTokens = await _tokenManager.hasValidTokens();
    if (!hasTokens) {
      return TokenValidationResult.noToken();
    }

    try {
      final response = await get('/open/user/info');
      
      if (response.statusCode == 200 && response.data is Map<String, dynamic>) {
        final data = response.data as Map<String, dynamic>;
        if (data['state'] == true && data['code'] == 0) {
          return TokenValidationResult.success(
            wasRefreshed: response.extra['tokenRefreshed'] == true
          );
        }
      }
      
      return TokenValidationResult.unknownError('Token验证失败');
    } on DioException catch (e) {
      return TokenValidationResult.networkError('网络错误: ${e.message}');
    }
  }

  /// 清除所有Token
  Future<void> clearTokens() async {
    await _tokenManager.clearTokens();
  }

  /// 获取文件下载/播放地址
  Future<Response> getDownloadUrl(String pickCode) async {
    return await postFormData('/open/ufile/downurl', formData: {
      'pick_code': pickCode,
    });
  }

  /// 获取文件播放地址的便捷方法
  Future<String> getPlayUrl(String pickCode) async {
    final response = await getDownloadUrl(pickCode);
    
    if (response.statusCode != 200 || response.data is! Map<String, dynamic>) {
      throw Exception('Failed to get play URL: Invalid response');
    }
    
    final data = response.data as Map<String, dynamic>;
    
    if (data['code'] != null && data['code'] != 0) {
      throw Exception('Failed to get play URL: Error code ${data['code']}');
    }
    
    if (data['state'] == true && data['data'] != null) {
      final downloadData = data['data'] as Map<String, dynamic>;
      
      if (downloadData.isNotEmpty) {
        final fileId = downloadData.keys.first;
        final fileInfo = downloadData[fileId] as Map<String, dynamic>;
        
        if (fileInfo['url'] != null && fileInfo['url']['url'] != null) {
          return fileInfo['url']['url'].toString();
        }
      }
    }
    
    throw Exception('Failed to get play URL: Invalid response data');
  }

  /// 批量获取多个文件的下载/播放地址
  Future<Response> getBatchDownloadUrls(List<String> pickCodes) async {
    if (pickCodes.isEmpty) {
      throw Exception('Pick codes list cannot be empty');
    }
    
    final pickCodeString = pickCodes.join(',');
    return await postFormData('/open/ufile/downurl', formData: {
      'pick_code': pickCodeString,
    });
  }

  /// 批量获取文件播放地址的便捷方法
  Future<Map<String, String>> getBatchPlayUrls(List<String> pickCodes) async {
    final response = await getBatchDownloadUrls(pickCodes);
    
    if (response.statusCode != 200 || response.data is! Map<String, dynamic>) {
      throw Exception('Failed to get batch play URLs: Invalid response');
    }
    
    final data = response.data as Map<String, dynamic>;
    
    if (data['code'] != null && data['code'] != 0) {
      throw Exception('Failed to get batch play URLs: Error code ${data['code']}');
    }
    
    if (data['state'] == true && data['data'] != null) {
      final downloadData = data['data'] as Map<String, dynamic>;
      final result = <String, String>{};
      
      for (final entry in downloadData.entries) {
        final fileInfo = entry.value as Map<String, dynamic>;
        final pickCode = fileInfo['pick_code']?.toString();
        final url = fileInfo['url']?['url']?.toString();
        
        if (pickCode != null && url != null) {
          result[pickCode] = url;
        }
      }
      
      return result;
    }
    
    throw Exception('Failed to get batch play URLs: Invalid response data');
  }

  /// 上传文件
  Future<Response> uploadFile({
    required String filePath,
    required String fileName,
    String? uploadPath,
    ProgressCallback? onSendProgress,
    CancelToken? cancelToken,
  }) async {
    final formData = FormData.fromMap({
      'file': await MultipartFile.fromFile(filePath, filename: fileName),
      if (uploadPath != null) 'path': uploadPath,
    });

    return await _dio.post(
      '/open/ufile/upload',
      data: formData,
      onSendProgress: onSendProgress,
      cancelToken: cancelToken,
      options: Options(
        contentType: 'multipart/form-data',
      ),
    );
  }

  /// 获取Token状态（调试用）
  Future<Map<String, dynamic>> getTokenStatus() async {
    return await _tokenManager.getTokenStatus();
  }

  /// 获取用户信息
  Future<Map<String, dynamic>?> getUserInfo() async {
    return await _tokenManager.getUserInfo();
  }

  /// 销毁服务
  void dispose() {
    // 清理资源（如果需要）
  }
} 
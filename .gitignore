# Miscellaneous
*.class
*.log
*.pyc
*.swp
.DS_Store
.atom/
.build/
.buildlog/
.history
.svn/
.swiftpm/
migrate_working_dir/

# IntelliJ related
*.iml
*.ipr
*.iws
.idea/

# The .vscode folder contains launch configuration and tasks you configure in
# VS Code which you may wish to be included in version control, so this line
# is commented out by default.
#.vscode/

# Flutter/Dart/Pub related
**/doc/api/
**/ios/Flutter/.last_build_id
.dart_tool/
.flutter-plugins
.flutter-plugins-dependencies
.pub-cache/
.pub/
/build/

# Web related
/web/build/

# Symbolication related
app.*.symbols

# Obfuscation related
app.*.map.json

# Android Studio will place build artifacts here
/android/app/debug
/android/app/profile
/android/app/release

# Android related
/android/local.properties
/android/key.properties
/android/app/upload-keystore.jks
/android/app/debug.keystore
/android/.gradle/
/android/captures/
/android/gradlew
/android/gradlew.bat
/android/gradle/wrapper/gradle-wrapper.jar

# iOS related
/ios/Flutter/flutter_export_environment.sh
/ios/Flutter/Generated.xcconfig
/ios/Flutter/flutter_export_environment.sh
/ios/ServiceDefinitions.json
/ios/Runner/GeneratedPluginRegistrant.*
/ios/Pods/
/ios/.symlinks/
/ios/Flutter/App.framework
/ios/Flutter/Flutter.framework
/ios/Flutter/Flutter.podspec

# macOS related
/macos/Flutter/GeneratedPluginRegistrant.swift
/macos/Flutter/flutter_export_environment.sh

# Windows related
/windows/flutter/generated_plugin_registrant.cc
/windows/flutter/generated_plugin_registrant.h

# Linux related
/linux/flutter/generated_plugin_registrant.cc
/linux/flutter/generated_plugin_registrant.h

# Coverage
coverage/
lcov.info

# Test related
/test_driver/
/integration_test/driver/

# Environment files
.env
.env.local
.env.development
.env.production

# Temporary files
*.tmp
*.temp
*~

# Log files
*.log
logs/

# Cache directories
.cache/
.tmp/

# OS generated files
Thumbs.db
ehthumbs.db
Desktop.ini

# Firebase (if used)
/ios/firebase_app_id_file.json
/android/app/google-services.json
/ios/Runner/GoogleService-Info.plist
/web/firebase-config.js

# Fastlane (if used)
/ios/fastlane/report.xml
/ios/fastlane/Preview.html
/ios/fastlane/screenshots
/ios/fastlane/test_output
/android/fastlane/report.xml
/android/fastlane/Preview.html
/android/fastlane/screenshots
/android/fastlane/test_output

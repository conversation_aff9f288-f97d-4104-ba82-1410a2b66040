import 'dart:async';
import 'package:dio/dio.dart';
import 'package:get_it/get_it.dart';
import 'token_manager.dart';

/// 认证拦截器 - 自动处理Token添加和刷新
/// 所有HTTP日志记录现在由TalkerDioLogger自动处理
class AuthInterceptor extends Interceptor {
  final TokenManager _tokenManager = GetIt.instance<TokenManager>();
  
  // 用于防止并发刷新
  static const Duration _lockTimeout = Duration(seconds: 30);
  static Completer<void>? _refreshLock;

  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) async {
    try {
      // 检查是否需要认证（排除刷新token的请求）
      if (!_needsAuth(options.path)) {
        handler.next(options);
        return;
      }

      // 获取token
      final token = await _tokenManager.getAccessToken();
      if (token != null) {
        options.headers['Authorization'] = 'Bearer $token';
        
        // 检查token是否即将过期
        if (await _tokenManager.isTokenExpiringSoon()) {
          // 主动刷新token（不阻塞当前请求）
          // TalkerDioLogger会自动记录刷新请求
          unawaited(_tokenManager.refreshAccessToken());
        }
      }

      handler.next(options);
    } catch (e) {
      // 错误会被TalkerDioLogger自动记录
      handler.next(options);
    }
  }

  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) async {
    try {
      // 检查响应中是否包含token失效的业务错误码
      if (_shouldRefreshTokenFromResponse(response)) {
        // 使用锁防止并发刷新
        await _ensureTokenRefresh();

        // 重试请求
        final retryResponse = await _retryRequest(response.requestOptions);
        if (retryResponse != null) {
          handler.resolve(retryResponse);
          return;
        }
      }
    } catch (e) {
      // 错误会被TalkerDioLogger自动记录
    }

    handler.next(response);
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) async {
    try {
      // 检查是否是401错误或者需要刷新token的错误
      if (_shouldRefreshToken(err)) {
        // 使用锁防止并发刷新
        await _ensureTokenRefresh();
        
        // 重试请求
        final retryResponse = await _retryRequest(err.requestOptions);
        if (retryResponse != null) {
          handler.resolve(retryResponse);
          return;
        }
      }
    } catch (e) {
      // 错误会被TalkerDioLogger自动记录
    }
    
    handler.next(err);
  }

  /// 检查请求是否需要认证
  bool _needsAuth(String path) {
    // 排除不需要认证的路径
    final publicPaths = [
      '/open/refreshToken',
      '/auth/login',
      '/health',
    ];
    
    return !publicPaths.any((publicPath) => path.contains(publicPath));
  }

  /// 检查是否应该刷新token（基于错误响应）
  bool _shouldRefreshToken(DioException err) {
    // HTTP 401错误
    if (err.response?.statusCode == 401) {
      return true;
    }

    // 检查业务错误码
    if (err.response?.data is Map<String, dynamic>) {
      final data = err.response!.data as Map<String, dynamic>;
      return _tokenManager.needsTokenRefresh(data);
    }

    return false;
  }

  /// 检查是否应该刷新token（基于正常响应）
  bool _shouldRefreshTokenFromResponse(Response response) {
    // 只检查状态码为200的响应
    if (response.statusCode != 200) {
      return false;
    }

    // 检查业务错误码
    if (response.data is Map<String, dynamic>) {
      final data = response.data as Map<String, dynamic>;
      return _tokenManager.needsTokenRefresh(data);
    }

    return false;
  }

  /// 确保token刷新（带锁机制）
  Future<void> _ensureTokenRefresh() async {
    // 如果已经有刷新在进行中，等待完成
    if (_refreshLock != null && !_refreshLock!.isCompleted) {
      try {
        await _refreshLock!.future.timeout(_lockTimeout);
      } catch (e) {
        // 超时错误会被TalkerDioLogger记录
      }
      return;
    }

    // 创建新的刷新锁
    _refreshLock = Completer<void>();
    
    try {
      final newToken = await _tokenManager.refreshAccessToken();
      if (newToken == null) {
        throw Exception('Token refresh failed');
      }
        
    } catch (e) {
      // 刷新失败会被TalkerDioLogger记录
      rethrow;
    } finally {
      _refreshLock?.complete();
      _refreshLock = null;
    }
  }

  /// 重试请求
  Future<Response?> _retryRequest(RequestOptions requestOptions) async {
    try {
      // 更新token
      final newToken = await _tokenManager.getAccessToken();
      if (newToken != null) {
        requestOptions.headers['Authorization'] = 'Bearer $newToken';
      }

      // 创建新的Dio实例来避免循环拦截
      final dio = Dio(BaseOptions(
        baseUrl: requestOptions.baseUrl,
        connectTimeout: requestOptions.connectTimeout,
        receiveTimeout: requestOptions.receiveTimeout,
        headers: requestOptions.headers,
      ));

      final response = await dio.request(
        requestOptions.path,
        data: requestOptions.data,
        queryParameters: requestOptions.queryParameters,
        options: Options(
          method: requestOptions.method,
          headers: requestOptions.headers,
          responseType: requestOptions.responseType,
          contentType: requestOptions.contentType,
        ),
      );

      return response;
    } catch (e) {
      // 重试失败会被TalkerDioLogger记录
      return null;
    }
  }
}

/// 重试拦截器 - 处理网络错误重试
/// 日志记录由TalkerDioLogger自动处理
class RetryInterceptor extends Interceptor {
  final int maxRetries;
  final Duration retryDelay;

  RetryInterceptor({
    this.maxRetries = 3,
    this.retryDelay = const Duration(seconds: 1),
  });

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) async {
    if (_shouldRetry(err) && _getRetryCount(err.requestOptions) < maxRetries) {
      try {
        await Future.delayed(retryDelay);
        
        _incrementRetryCount(err.requestOptions);

        // 重试请求 - TalkerDioLogger会自动记录
        final dio = Dio();
        final response = await dio.request(
          err.requestOptions.path,
          options: Options(
            method: err.requestOptions.method,
            headers: err.requestOptions.headers,
          ),
          data: err.requestOptions.data,
          queryParameters: err.requestOptions.queryParameters,
        );

        handler.resolve(response);
        return;
      } catch (e) {
        // 重试失败会被TalkerDioLogger记录
      }
    }
    
    handler.next(err);
  }

  /// 检查是否应该重试
  bool _shouldRetry(DioException err) {
    // 只对网络错误进行重试，不对业务错误重试
    return err.type == DioExceptionType.connectionTimeout ||
           err.type == DioExceptionType.receiveTimeout ||
           err.type == DioExceptionType.connectionError;
  }

  /// 获取重试次数
  int _getRetryCount(RequestOptions options) {
    return options.extra['retryCount'] as int? ?? 0;
  }

  /// 增加重试次数
  int _incrementRetryCount(RequestOptions options) {
    final count = _getRetryCount(options) + 1;
    options.extra['retryCount'] = count;
    return count;
  }
} 
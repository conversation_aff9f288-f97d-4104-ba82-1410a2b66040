import 'package:flutter/material.dart';

/// 错误显示组件类型
enum ErrorType {
  network,      // 网络错误
  timeout,      // 超时错误
  server,       // 服务器错误
  auth,         // 认证错误
  unknown,      // 未知错误
}

/// 通用错误显示组件
class ErrorDisplay extends StatelessWidget {
  final String? message;
  final ErrorType type;
  final VoidCallback? onRetry;
  final String? customRetryText;
  final Widget? customIcon;
  final bool showDetails;

  const ErrorDisplay({
    super.key,
    this.message,
    this.type = ErrorType.unknown,
    this.onRetry,
    this.customRetryText,
    this.customIcon,
    this.showDetails = false,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final errorInfo = _getErrorInfo();

    return Container(
      padding: const EdgeInsets.all(24),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // 错误图标
          customIcon ?? Icon(
            errorInfo.icon,
            size: 64,
            color: theme.colorScheme.error,
          ),
          const SizedBox(height: 16),
          
          // 错误标题
          Text(
            errorInfo.title,
            style: theme.textTheme.headlineSmall?.copyWith(
              color: theme.colorScheme.error,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          
          // 错误描述
          Text(
            errorInfo.description,
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
            ),
            textAlign: TextAlign.center,
          ),
          
          // 详细错误信息（可选）
          if (showDetails && message != null && message!.isNotEmpty) ...[
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: theme.colorScheme.errorContainer.withValues(alpha: 0.3),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: theme.colorScheme.error.withValues(alpha: 0.3),
                ),
              ),
              child: Text(
                message!,
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.colorScheme.onErrorContainer,
                  fontFamily: 'monospace',
                ),
                textAlign: TextAlign.left,
              ),
            ),
          ],
          
          // 重试按钮
          if (onRetry != null) ...[
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: onRetry,
              icon: const Icon(Icons.refresh),
              label: Text(customRetryText ?? '重试'),
              style: ElevatedButton.styleFrom(
                backgroundColor: theme.colorScheme.primary,
                foregroundColor: theme.colorScheme.onPrimary,
                padding: const EdgeInsets.symmetric(
                  horizontal: 24,
                  vertical: 12,
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  /// 根据错误类型获取错误信息
  _ErrorInfo _getErrorInfo() {
    switch (type) {
      case ErrorType.network:
        return const _ErrorInfo(
          icon: Icons.wifi_off,
          title: '网络连接失败',
          description: '请检查您的网络连接，然后重试',
        );
      case ErrorType.timeout:
        return const _ErrorInfo(
          icon: Icons.timer_off,
          title: '请求超时',
          description: '网络响应缓慢，请稍后重试',
        );
      case ErrorType.server:
        return const _ErrorInfo(
          icon: Icons.error_outline,
          title: '服务器错误',
          description: '服务器暂时无法响应，请稍后重试',
        );
      case ErrorType.auth:
        return const _ErrorInfo(
          icon: Icons.lock_outline,
          title: '认证失败',
          description: '您的登录信息已过期，请重新登录',
        );
      case ErrorType.unknown:
        return const _ErrorInfo(
          icon: Icons.help_outline,
          title: '出现了问题',
          description: '遇到了未知错误，请重试或联系支持',
        );
    }
  }
}

/// 错误信息数据类
class _ErrorInfo {
  final IconData icon;
  final String title;
  final String description;

  const _ErrorInfo({
    required this.icon,
    required this.title,
    required this.description,
  });
}

/// 加载失败的简化错误组件
class LoadingErrorWidget extends StatelessWidget {
  final String? error;
  final VoidCallback? onRetry;

  const LoadingErrorWidget({
    super.key,
    this.error,
    this.onRetry,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: ErrorDisplay(
        message: error,
        type: _detectErrorType(error),
        onRetry: onRetry,
        showDetails: false,
      ),
    );
  }

  /// 根据错误消息检测错误类型
  ErrorType _detectErrorType(String? error) {
    if (error == null) return ErrorType.unknown;
    
    final lowerError = error.toLowerCase();
    
    if (lowerError.contains('network') || 
        lowerError.contains('connection') ||
        lowerError.contains('socket')) {
      return ErrorType.network;
    }
    
    if (lowerError.contains('timeout') || 
        lowerError.contains('time out')) {
      return ErrorType.timeout;
    }
    
    if (lowerError.contains('401') || 
        lowerError.contains('unauthorized') ||
        lowerError.contains('token')) {
      return ErrorType.auth;
    }
    
    if (lowerError.contains('500') || 
        lowerError.contains('server') ||
        lowerError.contains('internal')) {
      return ErrorType.server;
    }
    
    return ErrorType.unknown;
  }
}

/// 列表底部的加载指示器
class ListLoadingIndicator extends StatelessWidget {
  final bool isLoading;
  final bool hasError;
  final String? error;
  final VoidCallback? onRetry;

  const ListLoadingIndicator({
    super.key,
    required this.isLoading,
    this.hasError = false,
    this.error,
    this.onRetry,
  });

  @override
  Widget build(BuildContext context) {
    if (hasError) {
      return Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.error_outline,
              color: Theme.of(context).colorScheme.error,
              size: 32,
            ),
            const SizedBox(height: 8),
            Text(
              error ?? '加载失败',
              style: TextStyle(
                color: Theme.of(context).colorScheme.error,
                fontSize: 14,
              ),
              textAlign: TextAlign.center,
            ),
            if (onRetry != null) ...[
              const SizedBox(height: 8),
              TextButton.icon(
                onPressed: onRetry,
                icon: const Icon(Icons.refresh, size: 16),
                label: const Text('重试'),
                style: TextButton.styleFrom(
                  foregroundColor: Theme.of(context).colorScheme.primary,
                ),
              ),
            ],
          ],
        ),
      );
    }

    if (isLoading) {
      return Container(
        padding: const EdgeInsets.all(16),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            SizedBox(
              width: 16,
              height: 16,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(
                  Theme.of(context).colorScheme.primary,
                ),
              ),
            ),
            const SizedBox(width: 12),
            Text(
              '加载更多...',
              style: TextStyle(
                color: Theme.of(context).colorScheme.primary,
                fontSize: 14,
              ),
            ),
          ],
        ),
      );
    }

    return const SizedBox.shrink();
  }
} 
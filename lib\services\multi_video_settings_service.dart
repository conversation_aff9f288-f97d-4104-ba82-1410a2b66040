import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// 多视频播放设置服务
/// 管理多个视频同时播放的相关设置
class MultiVideoSettingsService extends ChangeNotifier {
  // SharedPreferences 键名
  static const String _enabledKey = 'multi_video_enabled';
  static const String _maxPlayersKey = 'multi_video_max_players';
  
  // 私有字段
  bool _isEnabled = false;
  int _maxPlayers = 2;
  bool _isInitialized = false;
  
  // 公共访问器
  bool get isEnabled => _isEnabled;
  int get maxPlayers => _maxPlayers;
  bool get isInitialized => _isInitialized;
  
  /// 单例模式
  static final MultiVideoSettingsService _instance = MultiVideoSettingsService._internal();
  factory MultiVideoSettingsService() => _instance;
  MultiVideoSettingsService._internal();
  
  /// 初始化服务
  Future<void> initialize() async {
    if (_isInitialized) return;
    
    try {
      await _loadSettings();
      _isInitialized = true;
    } catch (e) {
      // 如果加载失败，使用默认值
      _isEnabled = false;
      _maxPlayers = 2;
      _isInitialized = true;
    }
  }
  
  /// 从SharedPreferences加载设置
  Future<void> _loadSettings() async {
    final prefs = await SharedPreferences.getInstance();
    _isEnabled = prefs.getBool(_enabledKey) ?? false;
    _maxPlayers = prefs.getInt(_maxPlayersKey) ?? 2;
  }
  
  /// 设置多视频播放启用状态
  Future<void> setEnabled(bool enabled) async {
    if (_isEnabled != enabled) {
      _isEnabled = enabled;
      notifyListeners();
      
      try {
        final prefs = await SharedPreferences.getInstance();
        await prefs.setBool(_enabledKey, enabled);
      } catch (e) {
        debugPrint('Failed to save multi video enabled setting: $e');
      }
    }
  }
  
  /// 设置最大同时播放数量
  Future<void> setMaxPlayers(int maxPlayers) async {
    if (_maxPlayers != maxPlayers && maxPlayers >= 1 && maxPlayers <= 6) {
      _maxPlayers = maxPlayers;
      notifyListeners();
      
      try {
        final prefs = await SharedPreferences.getInstance();
        await prefs.setInt(_maxPlayersKey, maxPlayers);
      } catch (e) {
        debugPrint('Failed to save multi video max players setting: $e');
      }
    }
  }
  
  /// 重置为默认设置
  Future<void> resetToDefaults() async {
    _isEnabled = false;
    _maxPlayers = 2;
    notifyListeners();

    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_enabledKey, false);
      await prefs.setInt(_maxPlayersKey, 2);
    } catch (e) {
      debugPrint('Failed to reset multi video settings: $e');
    }
  }
  
  /// 获取设置摘要信息
  String get settingsSummary {
    if (!_isEnabled) {
      return '已禁用';
    }
    return '已启用 (最多同时播放 $_maxPlayers 个视频)';
  }

  /// 重置服务状态（仅用于测试）
  void resetForTesting() {
    _isEnabled = false;
    _maxPlayers = 2;
    _isInitialized = false;
  }
}

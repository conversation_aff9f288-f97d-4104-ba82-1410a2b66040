import 'package:flutter/material.dart';
import 'dart:async';
import '../services/video_frame_extraction_manager.dart';

/// 视频帧提取测试页面
/// 用于测试和演示锁机制和限流功能
class VideoFrameTestPage extends StatefulWidget {
  const VideoFrameTestPage({super.key});

  @override
  State<VideoFrameTestPage> createState() => _VideoFrameTestPageState();
}

class _VideoFrameTestPageState extends State<VideoFrameTestPage> {
  final VideoFrameExtractionManager _frameManager = VideoFrameExtractionManager();
  final List<String> _logs = [];
  final ScrollController _scrollController = ScrollController();
  Timer? _statusTimer;
  Map<String, dynamic> _statistics = {};

  // 测试用的视频URL（这里使用示例URL，实际使用时需要替换为真实的视频URL）
  final List<String> _testVideoUrls = [
    'https://example.com/video1.mp4',
    'https://example.com/video2.mp4',
    'https://example.com/video3.mp4',
  ];

  @override
  void initState() {
    super.initState();
    _startStatusUpdates();
    _updateStatistics();
  }

  @override
  void dispose() {
    _statusTimer?.cancel();
    super.dispose();
  }

  void _startStatusUpdates() {
    _statusTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (mounted) {
        _updateStatistics();
      }
    });
  }

  void _updateStatistics() {
    setState(() {
      _statistics = _frameManager.getStatistics();
    });
  }

  void _addLog(String message) {
    setState(() {
      final timestamp = DateTime.now().toString().substring(11, 19);
      _logs.add('[$timestamp] $message');
    });
    
    // 自动滚动到底部
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  void _clearLogs() {
    setState(() {
      _logs.clear();
    });
  }

  void _resetStatistics() {
    _frameManager.resetStatistics();
    _updateStatistics();
    _addLog('统计信息已重置');
  }

  /// 测试单个帧提取
  Future<void> _testSingleFrameExtraction(String videoUrl) async {
    _addLog('开始测试单帧提取: $videoUrl');
    
    final frameData = await _frameManager.extractFrame(
      videoUrl: videoUrl,
      timeMs: 5000, // 5秒位置
      maxWidth: 200,
      maxHeight: 200,
      quality: 75,
      onProgress: (url, timeMs) {
        _addLog('提取进度: $url 时间点 ${timeMs}ms');
      },
    );
    
    if (frameData != null) {
      _addLog('单帧提取成功: ${frameData.length} bytes');
    } else {
      _addLog('单帧提取失败');
    }
  }

  /// 测试批量帧提取
  Future<void> _testBatchFrameExtraction(String videoUrl) async {
    _addLog('开始测试批量帧提取: $videoUrl');
    
    final timePositions = [1000, 3000, 5000, 7000, 9000]; // 5个时间点
    
    final frames = await _frameManager.extractFrames(
      videoUrl: videoUrl,
      timePositions: timePositions,
      maxWidth: 200,
      maxHeight: 200,
      quality: 75,
      onProgress: (current, total, timeMs) {
        _addLog('批量提取进度: $current/$total 帧，时间点: ${timeMs}ms');
      },
      onFrameExtracted: (index, frameData) {
        if (frameData != null) {
          _addLog('第${index + 1}帧提取成功: ${frameData.length} bytes');
        } else {
          _addLog('第${index + 1}帧提取失败');
        }
      },
    );
    
    _addLog('批量帧提取完成: 成功${frames.length}/${timePositions.length}帧');
  }

  /// 测试并发提取（验证锁机制）
  Future<void> _testConcurrentExtraction() async {
    _addLog('开始测试并发提取（验证锁机制）');
    
    // 同时启动多个提取任务
    final futures = _testVideoUrls.map((url) => _testSingleFrameExtraction(url));
    
    await Future.wait(futures);
    _addLog('并发提取测试完成');
  }

  /// 测试限流机制
  Future<void> _testRateLimiting() async {
    _addLog('开始测试限流机制（连续提取多帧）');
    
    final videoUrl = _testVideoUrls.first;
    final timePositions = [1000, 2000, 3000, 4000, 5000, 6000, 7000, 8000];
    
    for (int i = 0; i < timePositions.length; i++) {
      final startTime = DateTime.now();
      
      await _frameManager.extractFrame(
        videoUrl: videoUrl,
        timeMs: timePositions[i],
        maxWidth: 200,
        maxHeight: 200,
        quality: 75,
      );
      
      final endTime = DateTime.now();
      final duration = endTime.difference(startTime);
      
      _addLog('第${i + 1}帧提取耗时: ${duration.inMilliseconds}ms');
    }
    
    _addLog('限流测试完成');
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('视频帧提取测试'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _resetStatistics,
            tooltip: '重置统计',
          ),
          IconButton(
            icon: const Icon(Icons.clear),
            onPressed: _clearLogs,
            tooltip: '清空日志',
          ),
        ],
      ),
      body: Column(
        children: [
          // 统计信息卡片
          Card(
            margin: const EdgeInsets.all(8),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '提取统计',
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                  const SizedBox(height: 8),
                  Wrap(
                    spacing: 16,
                    runSpacing: 8,
                    children: [
                      _buildStatItem('总提取次数', _statistics['totalExtractions']?.toString() ?? '0'),
                      _buildStatItem('成功次数', _statistics['successfulExtractions']?.toString() ?? '0'),
                      _buildStatItem('失败次数', _statistics['failedExtractions']?.toString() ?? '0'),
                      _buildStatItem('成功率', _statistics['successRate']?.toString() ?? '0%'),
                      _buildStatItem('正在提取', _statistics['isCurrentlyExtracting'] == true ? '是' : '否'),
                      _buildStatItem('限流间隔', '${_statistics['frameIntervalMs'] ?? 0}ms'),
                    ],
                  ),
                  if (_statistics['currentVideo'] != null) ...[
                    const SizedBox(height: 8),
                    Text(
                      '当前提取: ${_statistics['currentVideo']}',
                      style: Theme.of(context).textTheme.bodySmall,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ],
              ),
            ),
          ),
          
          // 测试按钮
          Card(
            margin: const EdgeInsets.symmetric(horizontal: 8),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '测试功能',
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                  const SizedBox(height: 8),
                  Wrap(
                    spacing: 8,
                    runSpacing: 8,
                    children: [
                      ElevatedButton(
                        onPressed: () => _testSingleFrameExtraction(_testVideoUrls.first),
                        child: const Text('单帧提取'),
                      ),
                      ElevatedButton(
                        onPressed: () => _testBatchFrameExtraction(_testVideoUrls.first),
                        child: const Text('批量提取'),
                      ),
                      ElevatedButton(
                        onPressed: _testConcurrentExtraction,
                        child: const Text('并发测试'),
                      ),
                      ElevatedButton(
                        onPressed: _testRateLimiting,
                        child: const Text('限流测试'),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
          
          // 日志区域
          Expanded(
            child: Card(
              margin: const EdgeInsets.all(8),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Padding(
                    padding: const EdgeInsets.all(16),
                    child: Text(
                      '操作日志',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                  ),
                  Expanded(
                    child: ListView.builder(
                      controller: _scrollController,
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      itemCount: _logs.length,
                      itemBuilder: (context, index) {
                        return Padding(
                          padding: const EdgeInsets.symmetric(vertical: 2),
                          child: Text(
                            _logs[index],
                            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              fontFamily: 'monospace',
                            ),
                          ),
                        );
                      },
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem(String label, String value) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: Colors.grey[600],
          ),
        ),
        Text(
          value,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }
}

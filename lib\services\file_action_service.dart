import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import '../models/file_item.dart';
import '../utils/file_type_utils.dart';
import '../pages/file_list_page.dart';
import '../pages/image_viewer_page.dart';
import '../pages/media_kit_video_player_page.dart';
import '../pages/multi_video_player_page.dart';
import '../services/multi_video_settings_service.dart';

/// 文件操作服务
/// 负责处理文件点击、打开、导航等操作
class FileActionService {
  
  /// 处理文件项点击事件
  static void handleFileItemTap(
    BuildContext context,
    FileItem item,
    List<FileItem> allFiles,
  ) {
    if (item.isFolder) {
      _navigateToFolder(context, item);
    } else {
      final fileType = FileTypeUtils.getFileType(item);
      
      switch (fileType) {
        case FileType.image:
          _openImageViewer(context, item, allFiles);
          break;
        case FileType.video:
          _openVideoPlayer(context, item, allFiles);
          break;
        case FileType.audio:
          _openAudioPlayer(context, item, allFiles);
          break;
        default:
          _showFileInfo(context, item);
      }
    }
  }

  /// 导航到文件夹
  static void _navigateToFolder(BuildContext context, FileItem folder) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => FileListPage(
          cid: folder.id,
          title: folder.name,
        ),
      ),
    );
  }

  /// 打开图片查看器
  static void _openImageViewer(
    BuildContext context,
    FileItem selectedImage,
    List<FileItem> allFiles,
  ) {
    // 获取当前目录中的所有图片文件
    final imageFiles = allFiles.where(FileTypeUtils.isImageFile).toList();
    
    // 找到被点击图片在图片列表中的索引
    final initialIndex = imageFiles.indexOf(selectedImage);
    
    if (initialIndex != -1) {
      Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => ImageViewerPage(
            images: imageFiles,
            initialIndex: initialIndex,
          ),
        ),
      );
    } else {
      // 如果找不到图片，显示错误信息
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('无法找到图片文件')),
      );
    }
  }

  /// 打开视频播放器
  static void _openVideoPlayer(
    BuildContext context,
    FileItem videoFile,
    List<FileItem> allFiles,
  ) {
    // 筛选当前目录中的所有视频文件
    final videoFiles = allFiles.where(FileTypeUtils.isVideoFile).toList();

    // 找到当前视频在播放列表中的索引
    final currentIndex = videoFiles.indexWhere((file) => file.id == videoFile.id);

    // 获取多视频播放设置
    final multiVideoSettings = GetIt.instance<MultiVideoSettingsService>();

    if (multiVideoSettings.isEnabled) {
      // 显示播放器选择对话框
      _showVideoPlayerSelector(context, videoFiles, currentIndex >= 0 ? currentIndex : 0);
    } else {
      // 直接使用单视频播放器
      Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => MediaKitVideoPlayerPage(
            files: videoFiles,
            initialIndex: currentIndex >= 0 ? currentIndex : 0,
          ),
        ),
      );
    }
  }

  /// 显示视频播放器选择对话框
  static void _showVideoPlayerSelector(
    BuildContext context,
    List<FileItem> videoFiles,
    int initialIndex,
  ) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('选择播放模式'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ListTile(
                leading: const Icon(Icons.play_circle_outline),
                title: const Text('单视频播放'),
                subtitle: const Text('传统的单个视频播放模式'),
                onTap: () {
                  Navigator.of(context).pop();
                  Navigator.of(context).push(
                    MaterialPageRoute(
                      builder: (context) => MediaKitVideoPlayerPage(
                        files: videoFiles,
                        initialIndex: initialIndex,
                      ),
                    ),
                  );
                },
              ),
              ListTile(
                leading: const Icon(Icons.video_library),
                title: const Text('多视频播放'),
                subtitle: const Text('同时播放多个视频'),
                onTap: () {
                  Navigator.of(context).pop();
                  Navigator.of(context).push(
                    MaterialPageRoute(
                      builder: (context) => MultiVideoPlayerPage(
                        files: videoFiles,
                        initialIndex: initialIndex,
                      ),
                    ),
                  );
                },
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('取消'),
            ),
          ],
        );
      },
    );
  }

  /// 打开音频播放器（待实现）
  static void _openAudioPlayer(
    BuildContext context,
    FileItem audioFile,
    List<FileItem> allFiles,
  ) {
    // TODO: 实现音频播放器
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('音频文件: ${audioFile.name}')),
    );
  }

  /// 显示文件信息
  static void _showFileInfo(BuildContext context, FileItem item) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('文件: ${item.name}')),
    );
  }

  /// 检查是否应该自动使用网格视图
  static bool shouldUseGridView(List<FileItem> files) {
    if (files.isEmpty) return false;
    
    // 计算图片文件的数量
    final imageFiles = files.where(FileTypeUtils.isImageFile).length;
    final totalFiles = files.where((item) => item.isFile).length;
    
    // 如果图片文件超过总文件数的50%，且至少有3个图片文件，建议使用网格视图
    return totalFiles > 0 && imageFiles >= 3 && (imageFiles / totalFiles) > 0.5;
  }

  /// 处理面包屑导航
  static void handleBreadcrumbNavigate(
    BuildContext context,
    String cid,
    String name,
    String? currentCid,
    List<PathItem> currentPath,
  ) {
    if (cid == currentCid) return; // 已经在当前目录
    
    if (cid == '0') {
      // 返回根目录 - 回到根页面
      Navigator.of(context).popUntil((route) => route.isFirst);
    } else {
      // 返回到指定的父级目录
      // 构建完整的路径列表（包含根目录）
      List<PathItem> fullPath = [
        PathItem(id: '0', name: '根目录'),
        ...currentPath,
      ];
      
      // 找到目标路径在完整路径中的位置
      int targetIndex = -1;
      for (int i = 0; i < fullPath.length; i++) {
        if (fullPath[i].id == cid) {
          targetIndex = i;
          break;
        }
      }
      
      if (targetIndex >= 0) {
        // 计算需要返回的级数
        // 当前页面对应 fullPath 的最后一个元素
        // 需要返回到 targetIndex 对应的页面
        int currentDepth = fullPath.length - 1; // 当前页面的深度
        int targetDepth = targetIndex; // 目标页面的深度
        int levelsToGoBack = currentDepth - targetDepth;
        
        if (levelsToGoBack > 0) {
          // 使用简单的循环弹出页面
          for (int i = 0; i < levelsToGoBack; i++) {
            Navigator.of(context).pop();
          }
        }
      }
    }
  }
} 
import 'package:flutter/material.dart';
import '../models/file_item.dart';
import '../services/video_frame_extraction_manager.dart';
import 'video_preview_item.dart';

/// 视频预览管理器
/// 负责管理视频预览的加载队列，限制同时加载的视频数量
class VideoPreviewManager extends ChangeNotifier {
  static const int maxConcurrentPreviews = 2;
  
  final Set<String> _loadingVideos = <String>{};
  final Set<String> _loadedVideos = <String>{};
  
  /// 检查视频是否应该自动播放预览（只读检查，不修改状态）
  bool shouldAutoPlay(FileItem item) {
    final itemId = item.id;

    // 如果已经在加载或已加载，返回true
    if (_loadingVideos.contains(itemId) || _loadedVideos.contains(itemId)) {
      return true;
    }

    // 如果当前加载数量未达到限制，可以开始加载
    return _loadingVideos.length < maxConcurrentPreviews;
  }

  /// 请求开始加载视频预览
  bool requestAutoPlay(FileItem item) {
    final itemId = item.id;

    // 如果已经在加载或已加载，返回true
    if (_loadingVideos.contains(itemId) || _loadedVideos.contains(itemId)) {
      return true;
    }

    // 如果当前加载数量未达到限制，可以开始加载
    if (_loadingVideos.length < maxConcurrentPreviews) {
      _loadingVideos.add(itemId);
      // 延迟通知，避免在构建过程中调用
      WidgetsBinding.instance.addPostFrameCallback((_) {
        notifyListeners();
      });
      return true;
    }

    return false;
  }
  
  /// 标记视频加载完成
  void markVideoLoaded(FileItem item) {
    final itemId = item.id;
    _loadingVideos.remove(itemId);
    _loadedVideos.add(itemId);

    // 延迟通知，避免在构建过程中调用
    WidgetsBinding.instance.addPostFrameCallback((_) {
      notifyListeners();
    });

    // 检查是否有等待加载的视频
    _tryLoadNextVideo();
  }

  /// 标记视频加载失败
  void markVideoFailed(FileItem item) {
    final itemId = item.id;
    _loadingVideos.remove(itemId);

    // 延迟通知，避免在构建过程中调用
    WidgetsBinding.instance.addPostFrameCallback((_) {
      notifyListeners();
    });

    // 检查是否有等待加载的视频
    _tryLoadNextVideo();
  }
  
  /// 尝试加载下一个等待中的视频
  void _tryLoadNextVideo() {
    // 这个方法会在视频预览列表中被调用
    // 由于我们无法直接访问视频列表，这里只是触发通知
    // 实际的加载逻辑会在VideoPreviewList中处理
  }
  
  /// 清除所有状态
  void clear() {
    _loadingVideos.clear();
    _loadedVideos.clear();

    // 延迟通知，避免在构建过程中调用
    WidgetsBinding.instance.addPostFrameCallback((_) {
      notifyListeners();
    });
  }
  
  /// 获取当前加载状态信息（用于调试）
  Map<String, dynamic> getStatus() {
    final frameManager = VideoFrameExtractionManager();
    final frameStats = frameManager.getStatistics();

    return {
      'loading': _loadingVideos.length,
      'loaded': _loadedVideos.length,
      'maxConcurrent': maxConcurrentPreviews,
      'frameExtractionActive': frameStats['isCurrentlyExtracting'] ?? false,
      'currentExtractionVideo': frameStats['currentVideo'],
      'frameExtractionStats': frameStats,
    };
  }
}

/// 视频预览列表组件
class VideoPreviewList extends StatefulWidget {
  final List<FileItem> videoFiles;
  final Map<String, String> playUrls;
  final Function(FileItem) onVideoTap;
  final ScrollController? scrollController;

  const VideoPreviewList({
    super.key,
    required this.videoFiles,
    required this.playUrls,
    required this.onVideoTap,
    this.scrollController,
  });

  @override
  State<VideoPreviewList> createState() => _VideoPreviewListState();
}

class _VideoPreviewListState extends State<VideoPreviewList> {
  late VideoPreviewManager _previewManager;
  
  @override
  void initState() {
    super.initState();
    _previewManager = VideoPreviewManager();
  }
  
  @override
  void dispose() {
    _previewManager.dispose();
    super.dispose();
  }
  
  @override
  void didUpdateWidget(VideoPreviewList oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    // 如果视频列表发生变化，清除管理器状态
    if (widget.videoFiles != oldWidget.videoFiles) {
      _previewManager.clear();
    }
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _previewManager,
      builder: (context, child) {
        return GridView.builder(
          controller: widget.scrollController,
          padding: const EdgeInsets.all(8),
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 2,
            crossAxisSpacing: 8,
            mainAxisSpacing: 8,
            childAspectRatio: 0.8,
          ),
          itemCount: widget.videoFiles.length,
          itemBuilder: (context, index) {
            final item = widget.videoFiles[index];
            final playUrl = widget.playUrls[item.pickCode];
            final shouldAutoPlay = _previewManager.shouldAutoPlay(item);
            
            return VideoPreviewItemWrapper(
              item: item,
              playUrl: playUrl,
              shouldAutoPlay: shouldAutoPlay,
              onTap: () => widget.onVideoTap(item),
              onLoadComplete: () => _previewManager.markVideoLoaded(item),
              onLoadFailed: () => _previewManager.markVideoFailed(item),
            );
          },
        );
      },
    );
  }
}

/// 视频预览项包装器
/// 用于处理加载完成/失败的回调
class VideoPreviewItemWrapper extends StatefulWidget {
  final FileItem item;
  final String? playUrl;
  final bool shouldAutoPlay;
  final VoidCallback onTap;
  final VoidCallback onLoadComplete;
  final VoidCallback onLoadFailed;

  const VideoPreviewItemWrapper({
    super.key,
    required this.item,
    this.playUrl,
    required this.shouldAutoPlay,
    required this.onTap,
    required this.onLoadComplete,
    required this.onLoadFailed,
  });

  @override
  State<VideoPreviewItemWrapper> createState() => _VideoPreviewItemWrapperState();
}

class _VideoPreviewItemWrapperState extends State<VideoPreviewItemWrapper> {
  bool _hasRequestedLoad = false;

  @override
  Widget build(BuildContext context) {
    // 如果应该自动播放但还没有请求加载，则请求加载
    if (widget.shouldAutoPlay && !_hasRequestedLoad) {
      _hasRequestedLoad = true;
      // 延迟到下一帧再请求，避免在构建过程中修改状态
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          // 这里可以添加实际的加载请求逻辑
        }
      });
    }

    return VideoPreviewItem(
      item: widget.item,
      playUrl: widget.playUrl,
      shouldAutoPlay: widget.shouldAutoPlay,
      onTap: widget.onTap,
      onLoadComplete: widget.onLoadComplete,
      onLoadFailed: widget.onLoadFailed,
    );
  }
}

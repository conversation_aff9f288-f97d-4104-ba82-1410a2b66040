import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:media_kit/media_kit.dart';
import 'package:media_kit_video/media_kit_video.dart';
import 'package:my_115_app/services/token_manager.dart';
import 'package:get_it/get_it.dart';

import '../models/file_item.dart';
import '../services/api_service.dart';
import '../services/vod_proxy_settings_service.dart';
import '../services/multi_video_settings_service.dart';
import '../utils/vod_proxy_service.dart';

/// 多视频播放器页面
class MultiVideoPlayerPage extends StatefulWidget {
  final List<FileItem> files;
  final int initialIndex;

  const MultiVideoPlayerPage({
    super.key,
    required this.files,
    required this.initialIndex,
  });

  @override
  State<MultiVideoPlayerPage> createState() => _MultiVideoPlayerPageState();
}

class _MultiVideoPlayerPageState extends State<MultiVideoPlayerPage> {
  final ApiService _apiService = ApiService();
  final VodProxySettingsService _vodProxySettings = GetIt.instance<VodProxySettingsService>();
  final MultiVideoSettingsService _multiVideoSettings = GetIt.instance<MultiVideoSettingsService>();

  bool _isLoading = true;
  bool _hasError = false;
  String _errorMessage = '';
  late List<FileItem> _videoFiles;
  late int _adjustedInitialIndex;
  bool _showPlaylist = false;

  // 多播放器相关
  List<Player> _players = [];
  List<VideoController> _controllers = [];
  List<int> _playingIndices = [];
  List<String> _playUrls = [];

  @override
  void initState() {
    super.initState();

    // 根据设置启动 VodProxy 代理服务器
    _initializeVodProxy();

    SystemChrome.setEnabledSystemUIMode(SystemUiMode.immersive);

    // 过滤出视频文件
    _videoFiles = widget.files.where((file) => file.isVideo && file.pickCode != null).toList();
    
    if (_videoFiles.isEmpty) {
      _hasError = true;
      _errorMessage = '没有找到可播放的视频文件';
      setState(() {
        _isLoading = false;
      });
      return;
    }

    // 调整初始索引到视频文件列表中的位置
    _adjustedInitialIndex = _findAdjustedIndex();

    // 初始化多播放器
    _initializeMultiPlayers();
  }

  /// 初始化VodProxy服务
  Future<void> _initializeVodProxy() async {
    if (_vodProxySettings.isEnabled) {
      try {
        await VodProxyService.startProxy(port: _vodProxySettings.port);
      } catch (e) {
        debugPrint('VodProxy启动失败: $e');
      }
    }
  }

  /// 找到初始视频在过滤后的视频列表中的位置
  int _findAdjustedIndex() {
    if (widget.initialIndex < 0 || widget.initialIndex >= widget.files.length) {
      return 0;
    }
    
    final originalFile = widget.files[widget.initialIndex];
    
    if (originalFile.isVideo && originalFile.pickCode != null) {
      for (int i = 0; i < _videoFiles.length; i++) {
        if (_videoFiles[i].id == originalFile.id) {
          return i;
        }
      }
    }
    
    return 0;
  }

  /// 初始化多播放器
  Future<void> _initializeMultiPlayers() async {
    try {
      // 提取所有视频的pick_code
      final pickCodes = _videoFiles
          .map((file) => file.pickCode!)
          .toList();

      // 批量获取播放地址
      final playUrls = await _apiService.getBatchPlayUrls(pickCodes);
      
      // 准备播放URL列表
      _playUrls = [];
      for (final file in _videoFiles) {
        final playUrl = playUrls[file.pickCode];
        if (playUrl != null) {
          String finalUrl = playUrl;

          // 如果启用了VodProxy，尝试获取代理URL
          if (_vodProxySettings.isEnabled) {
            try {
              final proxyUrl = await VodProxyService.getProxyUrl(
                playUrl,
                threadCount: _vodProxySettings.threadCount,
                chunkSize: _vodProxySettings.chunkSize,
              );
              if (proxyUrl.isNotEmpty) {
                finalUrl = proxyUrl;
              }
            } catch (e) {
              debugPrint('获取代理URL失败: $e');
            }
          }

          _playUrls.add(finalUrl);
        } else {
          _playUrls.add('error://failed_to_load_${file.pickCode}');
        }
      }

      if (_playUrls.isEmpty) {
        throw Exception('所有视频都无法获取播放地址');
      }

      // 创建初始播放器
      _createInitialPlayers();

      setState(() {
        _isLoading = false;
      });

    } catch (e) {
      setState(() {
        _isLoading = false;
        _hasError = true;
        _errorMessage = '视频加载失败: ${e.toString()}';
      });
    }
  }

  /// 创建初始播放器
  void _createInitialPlayers() {
    final maxPlayers = _multiVideoSettings.maxPlayers;
    
    // 从初始索引开始创建播放器
    for (int i = 0; i < maxPlayers && i < _videoFiles.length; i++) {
      final videoIndex = (_adjustedInitialIndex + i) % _videoFiles.length;
      _addPlayer(videoIndex);
    }
  }

  /// 添加播放器
  void _addPlayer(int videoIndex) {
    if (_players.length >= _multiVideoSettings.maxPlayers) return;
    if (_playingIndices.contains(videoIndex)) return;

    final player = Player(configuration: PlayerConfiguration(
      bufferSize: 256 * 1024 * 1024,
    ));
    
    final controller = VideoController(player);
    
    _players.add(player);
    _controllers.add(controller);
    _playingIndices.add(videoIndex);

    // 开始播放
    final media = Media(
      _playUrls[videoIndex],
      httpHeaders: {'User-Agent': TokenManager.userAgent},
    );
    
    player.open(media, play: true);
  }

  /// 移除播放器
  void _removePlayer(int playerIndex) {
    if (playerIndex < 0 || playerIndex >= _players.length) return;

    _players[playerIndex].dispose();
    _players.removeAt(playerIndex);
    _controllers.removeAt(playerIndex);
    _playingIndices.removeAt(playerIndex);
  }

  @override
  void dispose() {
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);
    
    // 释放所有播放器
    for (final player in _players) {
      player.dispose();
    }

    // 如果启用了VodProxy，停止服务
    if (_vodProxySettings.isEnabled) {
      VodProxyService.stopProxy().catchError((e) {
        debugPrint('停止VodProxy失败: $e');
      });
    }

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(color: Colors.white),
      );
    }

    if (_hasError) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error, color: Colors.red, size: 64),
            const SizedBox(height: 16),
            Text(
              _errorMessage,
              style: const TextStyle(color: Colors.white, fontSize: 16),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('返回'),
            ),
          ],
        ),
      );
    }

    return _buildMultiVideoView();
  }

  Widget _buildMultiVideoView() {
    return Stack(
      children: [
        // 多视频网格布局
        _buildVideoGrid(),
        
        // 控制按钮
        _buildControlButtons(),
        
        // 播放列表侧边栏
        if (_showPlaylist) _buildPlaylistDrawer(),
      ],
    );
  }

  Widget _buildVideoGrid() {
    final playerCount = _players.length;

    if (playerCount == 1) {
      return Center(child: Video(controller: _controllers[0]));
    }

    // 根据播放器数量决定网格布局
    int crossAxisCount = 2;
    if (playerCount <= 2) {
      crossAxisCount = 1;
    } else if (playerCount <= 4) {
      crossAxisCount = 2;
    } else {
      crossAxisCount = 3;
    }

    return GridView.builder(
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: crossAxisCount,
        childAspectRatio: 16 / 9,
        crossAxisSpacing: 2,
        mainAxisSpacing: 2,
      ),
      itemCount: _controllers.length,
      itemBuilder: (context, index) {
        return Container(
          decoration: BoxDecoration(
            border: Border.all(color: Colors.white24, width: 1),
          ),
          child: Stack(
            children: [
              Video(controller: _controllers[index]),
              // 视频标题
              Positioned(
                top: 8,
                left: 8,
                right: 8,
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.black54,
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Text(
                    _videoFiles[_playingIndices[index]].name,
                    style: const TextStyle(color: Colors.white, fontSize: 12),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ),
              // 关闭按钮
              if (_players.length > 1)
                Positioned(
                  top: 8,
                  right: 8,
                  child: GestureDetector(
                    onTap: () => setState(() => _removePlayer(index)),
                    child: Container(
                      padding: const EdgeInsets.all(4),
                      decoration: const BoxDecoration(
                        color: Colors.red,
                        shape: BoxShape.circle,
                      ),
                      child: const Icon(
                        Icons.close,
                        color: Colors.white,
                        size: 16,
                      ),
                    ),
                  ),
                ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildControlButtons() {
    return Positioned(
      bottom: 20,
      right: 20,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // 播放列表按钮
          FloatingActionButton(
            heroTag: "playlist",
            mini: true,
            backgroundColor: Colors.black54,
            onPressed: () => setState(() => _showPlaylist = !_showPlaylist),
            child: const Icon(Icons.playlist_play, color: Colors.white),
          ),
          const SizedBox(height: 8),
          // 添加视频按钮
          if (_players.length < _multiVideoSettings.maxPlayers && _players.length < _videoFiles.length)
            FloatingActionButton(
              heroTag: "add",
              mini: true,
              backgroundColor: Colors.green,
              onPressed: _showAddVideoDialog,
              child: const Icon(Icons.add, color: Colors.white),
            ),
          const SizedBox(height: 8),
          // 返回按钮
          FloatingActionButton(
            heroTag: "back",
            mini: true,
            backgroundColor: Colors.red,
            onPressed: () => Navigator.of(context).pop(),
            child: const Icon(Icons.arrow_back, color: Colors.white),
          ),
        ],
      ),
    );
  }

  Widget _buildPlaylistDrawer() {
    return Positioned(
      right: 0,
      top: 0,
      bottom: 0,
      child: Container(
        width: 300,
        decoration: const BoxDecoration(
          color: Colors.black87,
          border: Border(left: BorderSide(color: Colors.white24)),
        ),
        child: Column(
          children: [
            // 标题栏
            Container(
              padding: const EdgeInsets.all(16),
              decoration: const BoxDecoration(
                border: Border(bottom: BorderSide(color: Colors.white24)),
              ),
              child: Row(
                children: [
                  const Icon(Icons.playlist_play, color: Colors.white),
                  const SizedBox(width: 8),
                  const Text(
                    '播放列表',
                    style: TextStyle(color: Colors.white, fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                  const Spacer(),
                  IconButton(
                    icon: const Icon(Icons.close, color: Colors.white),
                    onPressed: () => setState(() => _showPlaylist = false),
                  ),
                ],
              ),
            ),
            // 视频列表
            Expanded(
              child: ListView.builder(
                itemCount: _videoFiles.length,
                itemBuilder: (context, index) {
                  final file = _videoFiles[index];
                  final isPlaying = _playingIndices.contains(index);

                  return Container(
                    decoration: BoxDecoration(
                      color: isPlaying ? Colors.blue.withValues(alpha: 0.3) : null,
                      border: const Border(bottom: BorderSide(color: Colors.white12)),
                    ),
                    child: ListTile(
                      dense: true,
                      leading: Icon(
                        Icons.video_file,
                        color: isPlaying ? Colors.blue : Colors.grey,
                        size: 20,
                      ),
                      title: Text(
                        file.name,
                        style: TextStyle(
                          color: isPlaying ? Colors.blue : Colors.white,
                          fontSize: 14,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                      subtitle: file.formattedSize.isNotEmpty
                          ? Text(
                              file.formattedSize,
                              style: TextStyle(
                                color: Colors.grey[400],
                                fontSize: 12,
                              ),
                            )
                          : null,
                      trailing: isPlaying
                          ? const Icon(Icons.volume_up, color: Colors.blue, size: 20)
                          : null,
                      onTap: () => _playVideoAtIndex(index),
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 显示添加视频对话框
  void _showAddVideoDialog() {
    final availableVideos = <int>[];
    for (int i = 0; i < _videoFiles.length; i++) {
      if (!_playingIndices.contains(i)) {
        availableVideos.add(i);
      }
    }

    if (availableVideos.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('所有视频都已在播放')),
      );
      return;
    }

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('选择要添加的视频'),
        content: SizedBox(
          width: double.maxFinite,
          height: 300,
          child: ListView.builder(
            itemCount: availableVideos.length,
            itemBuilder: (context, index) {
              final videoIndex = availableVideos[index];
              final file = _videoFiles[videoIndex];

              return ListTile(
                leading: const Icon(Icons.video_file),
                title: Text(file.name),
                subtitle: Text(file.formattedSize),
                onTap: () {
                  Navigator.of(context).pop();
                  setState(() => _addPlayer(videoIndex));
                },
              );
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
        ],
      ),
    );
  }

  /// 播放指定索引的视频
  void _playVideoAtIndex(int index) {
    if (_playingIndices.contains(index)) {
      // 如果已经在播放，不做任何操作
      return;
    }

    if (_players.length >= _multiVideoSettings.maxPlayers) {
      // 如果已达到最大播放数量，替换第一个播放器
      _removePlayer(0);
    }

    setState(() {
      _addPlayer(index);
      _showPlaylist = false;
    });
  }
}

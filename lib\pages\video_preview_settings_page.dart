import 'package:flutter/material.dart';
import '../services/video_preview_settings_service.dart';

/// 视频预览设置页面
class VideoPreviewSettingsPage extends StatefulWidget {
  const VideoPreviewSettingsPage({super.key});

  @override
  State<VideoPreviewSettingsPage> createState() => _VideoPreviewSettingsPageState();
}

class _VideoPreviewSettingsPageState extends State<VideoPreviewSettingsPage> {
  VideoPreviewSettings? _settings;
  bool _isLoading = true;
  bool _hasChanges = false;

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  Future<void> _loadSettings() async {
    try {
      final settings = await VideoPreviewSettingsService.getSettings();
      setState(() {
        _settings = settings;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('加载设置失败: $e')),
        );
      }
    }
  }

  Future<void> _saveSettings() async {
    if (_settings == null) return;

    try {
      await VideoPreviewSettingsService.saveSettings(_settings!);
      setState(() {
        _hasChanges = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('设置已保存'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('保存设置失败: $e')),
        );
      }
    }
  }

  Future<void> _resetToDefaults() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('重置设置'),
        content: const Text('确定要重置为默认设置吗？'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('确定'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await VideoPreviewSettingsService.resetToDefaults();
        await _loadSettings();
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('已重置为默认设置'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('重置失败: $e')),
          );
        }
      }
    }
  }

  void _updateSettings(VideoPreviewSettings newSettings) {
    setState(() {
      _settings = newSettings;
      _hasChanges = true;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('视频预览设置'),
        actions: [
          if (_hasChanges)
            TextButton(
              onPressed: _saveSettings,
              child: const Text('保存'),
            ),
          PopupMenuButton<String>(
            onSelected: (value) {
              if (value == 'reset') {
                _resetToDefaults();
              }
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'reset',
                child: Text('重置为默认'),
              ),
            ],
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _settings == null
              ? const Center(child: Text('加载设置失败'))
              : _buildSettingsContent(),
    );
  }

  Widget _buildSettingsContent() {
    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        _buildFrameCountSection(),
        const SizedBox(height: 24),
        _buildQualitySection(),
        const SizedBox(height: 24),
        _buildAnimationSection(),
        const SizedBox(height: 24),
        _buildInfoSection(),
      ],
    );
  }

  Widget _buildFrameCountSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '帧数设置',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            _buildSliderSetting(
              '最少帧数',
              _settings!.minFrameCount.toDouble(),
              3,
              15,
              (value) => _updateSettings(_settings!.copyWith(minFrameCount: value.round())),
              '${_settings!.minFrameCount} 帧',
            ),
            _buildSliderSetting(
              '目标帧数',
              _settings!.targetFrameCount.toDouble(),
              6,
              25,
              (value) => _updateSettings(_settings!.copyWith(targetFrameCount: value.round())),
              '${_settings!.targetFrameCount} 帧',
            ),
            _buildSliderSetting(
              '最多帧数',
              _settings!.maxFrameCount.toDouble(),
              10,
              30,
              (value) => _updateSettings(_settings!.copyWith(maxFrameCount: value.round())),
              '${_settings!.maxFrameCount} 帧',
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQualitySection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '质量设置',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            _buildSliderSetting(
              '图片质量',
              _settings!.frameQuality.toDouble(),
              30,
              100,
              (value) => _updateSettings(_settings!.copyWith(frameQuality: value.round())),
              '${_settings!.frameQuality}%',
            ),
            _buildSliderSetting(
              '图片尺寸',
              _settings!.frameSize.toDouble(),
              100,
              400,
              (value) => _updateSettings(_settings!.copyWith(frameSize: value.round())),
              '${_settings!.frameSize}px',
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAnimationSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '动画设置',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            const Text('播放速度'),
            const SizedBox(height: 8),
            SegmentedButton<String>(
              segments: const [
                ButtonSegment(value: 'slow', label: Text('慢')),
                ButtonSegment(value: 'normal', label: Text('正常')),
                ButtonSegment(value: 'fast', label: Text('快')),
              ],
              selected: {_settings!.animationSpeed},
              onSelectionChanged: (Set<String> selection) {
                _updateSettings(_settings!.copyWith(animationSpeed: selection.first));
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '说明',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            const Text(
              '• 帧数设置：控制从视频中提取多少帧用于预览\n'
              '• 最少帧数：短视频或提取失败时的最少帧数\n'
              '• 目标帧数：中等长度视频的目标帧数\n'
              '• 最多帧数：长视频的最大帧数\n'
              '• 图片质量：影响帧图片的清晰度和文件大小\n'
              '• 图片尺寸：帧图片的像素尺寸\n'
              '• 播放速度：帧切换的快慢',
              style: TextStyle(color: Colors.grey),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSliderSetting(
    String title,
    double value,
    double min,
    double max,
    ValueChanged<double> onChanged,
    String displayValue,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(title),
            Text(displayValue, style: const TextStyle(fontWeight: FontWeight.bold)),
          ],
        ),
        Slider(
          value: value,
          min: min,
          max: max,
          divisions: (max - min).round(),
          onChanged: onChanged,
        ),
        const SizedBox(height: 8),
      ],
    );
  }
}

# 多视频播放功能说明

## 功能概述

本应用现在支持多个视频同时播放功能，用户可以在设置中启用此功能，并配置同时播放的视频数量。

## 主要特性

### 1. 多视频播放设置服务 (MultiVideoSettingsService)

- **位置**: `lib/services/multi_video_settings_service.dart`
- **功能**: 管理多视频播放的相关设置
- **设置项**:
  - `isEnabled`: 是否启用多视频播放 (默认: false)
  - `maxPlayers`: 最大同时播放数量 (默认: 2, 范围: 1-6)

### 2. 多视频播放器页面 (MultiVideoPlayerPage)

- **位置**: `lib/pages/multi_video_player_page.dart`
- **功能**: 提供多视频同时播放的界面
- **特性**:
  - 网格布局显示多个视频
  - 根据播放器数量自动调整布局 (1列、2列或3列)
  - 每个视频窗口显示标题和关闭按钮
  - 支持添加/移除播放器
  - 播放列表侧边栏

### 3. 设置页面集成

- **位置**: `lib/pages/settings_page.dart`
- **新增设置项**: "多视频播放"
- **配置选项**:
  - 启用/禁用多视频播放
  - 调整最大同时播放数量 (滑块控制)
  - 重置为默认设置

### 4. 文件操作服务更新

- **位置**: `lib/services/file_action_service.dart`
- **新增功能**: 播放器选择对话框
- **行为**:
  - 当多视频播放启用时，点击视频文件会显示播放模式选择对话框
  - 用户可选择"单视频播放"或"多视频播放"
  - 当多视频播放禁用时，直接使用传统的单视频播放器

## 使用方法

### 启用多视频播放

1. 进入应用设置页面
2. 找到"多视频播放"选项
3. 开启开关启用功能
4. 调整"最大同时播放数量"滑块 (1-6个)

### 使用多视频播放

1. 确保多视频播放功能已启用
2. 点击任意视频文件
3. 在弹出的对话框中选择"多视频播放"
4. 在多视频播放器中:
   - 使用右下角的"+"按钮添加更多视频
   - 点击视频窗口右上角的"×"按钮关闭单个视频
   - 使用播放列表按钮查看和选择其他视频

### 控制按钮说明

- **播放列表按钮** (📋): 显示/隐藏播放列表侧边栏
- **添加视频按钮** (+): 添加新的视频到播放器
- **返回按钮** (←): 退出多视频播放器

## 技术实现

### 服务注册

多视频设置服务已在 `lib/services/service_locator.dart` 中注册为单例服务，并在应用启动时自动初始化。

### 播放器管理

- 每个视频使用独立的 `Player` 和 `VideoController` 实例
- 支持动态添加和移除播放器
- 自动处理资源释放和内存管理

### 布局适配

- 1个播放器: 居中显示
- 2个播放器: 1列布局 (上下排列)
- 3-4个播放器: 2列网格布局
- 5-6个播放器: 3列网格布局

## 注意事项

1. **性能影响**: 同时播放多个视频会消耗更多的系统资源 (CPU、内存、网络带宽)
2. **网络带宽**: 建议在良好的网络环境下使用多视频播放功能
3. **设备性能**: 低端设备可能无法流畅播放多个视频，建议减少同时播放的数量
4. **电池消耗**: 多视频播放会显著增加电池消耗

## 配置建议

- **高端设备**: 可设置最大播放数量为 4-6 个
- **中端设备**: 建议设置为 2-3 个
- **低端设备**: 建议设置为 1-2 个或禁用此功能

## 未来改进

1. 支持画中画模式
2. 添加音频混合控制
3. 支持视频窗口大小调整
4. 添加性能监控和自动优化
5. 支持视频同步播放

# 视频播放器使用指南

本应用现在支持两种视频播放器，用户可以根据需要选择最适合的播放器。

## 播放器选择

当点击视频文件时，会弹出播放器选择对话框，提供以下选项：

### 🆕 Media Kit 播放器（推荐）

基于 [media-kit](https://github.com/media-kit/media-kit) 库的现代化视频播放器。

**优势：**
- ✅ 支持更多视频格式和编解码器
- ✅ 硬件加速解码，性能更优
- ✅ 跨平台统一体验
- ✅ 内置播放列表管理
- ✅ 更好的错误处理

### 🔄 传统播放器

基于 video_player + chewie 的经典播放器。

**优势：**
- ✅ 稳定可靠，久经考验
- ✅ 适合常见格式（MP4, WebM 等）
- ✅ 兼容性好

## 支持的视频格式

### Media Kit 播放器支持的格式
- **常见格式**: MP4, AVI, MOV, MKV, WMV, FLV, M4V, MPG, MPEG, WebM
- **高级格式**: OGV, OGG, 3GP, 3G2, F4V, VOB, MTS, M2TS, TS, MXF
- **专业格式**: RM, RMVB, ASF, HEVC, H264, H265, VP8, VP9, AV1
- **容器格式**: MKA, MKS, DivX, XviD, DV, MP2, M1V, M2V

### 传统播放器支持的格式
- **主要格式**: MP4, AVI, MOV, MKV, WMV, FLV, M4V, MPG, MPEG, WebM

## 功能特性

### 播放控制
- ▶️ 播放/暂停
- ⏭️ 上一集/下一集
- 🔄 播放列表循环
- 📊 进度条拖拽
- 🔊 音量控制
- ⚡ 播放速度调节（Media Kit）

### 界面功能
- 🖥️ 全屏播放
- 📱 自适应横竖屏
- 🎭 自动隐藏控制器
- 📝 视频信息显示
- 📋 播放列表管理

### 播放列表功能
- 🎵 自动连播
- 🔀 随机播放（Media Kit）
- 📺 批量视频管理
- 🎯 快速跳转

## 使用建议

### 选择 Media Kit 播放器的情况：
- 播放高清/4K视频
- 播放特殊格式视频（MKV, HEVC等）
- 需要更好的性能表现
- 播放大型播放列表

### 选择传统播放器的情况：
- 播放常见格式视频（MP4, AVI等）
- 网络环境不稳定时
- 需要最大兼容性

## 技术说明

### Media Kit 播放器
- **底层**: 基于 libmpv 和 FFmpeg
- **渲染**: 硬件加速渲染
- **平台**: Android, iOS, Windows, macOS, Linux, Web
- **特点**: 原生性能，格式支持全面

### 传统播放器
- **底层**: 基于平台原生播放器
- **渲染**: 平台默认渲染器
- **平台**: Android, iOS, Web
- **特点**: 系统集成度高，稳定性好

## 故障排除

### 视频无法播放
1. 尝试切换到另一个播放器
2. 检查网络连接
3. 确认视频文件完整性
4. 查看错误信息详情

### 性能问题
1. 优先使用 Media Kit 播放器
2. 确保设备有足够存储空间
3. 关闭不必要的后台应用
4. 降低视频播放质量

### 格式不支持
1. 使用 Media Kit 播放器（支持更多格式）
2. 转换视频格式
3. 检查文件扩展名是否正确

## 更新日志

### v1.0.0
- ✨ 新增 Media Kit 播放器
- 🔧 优化播放器选择体验
- 📈 扩展支持的视频格式
- 🎯 改进播放列表功能
- 🐛 修复已知问题 
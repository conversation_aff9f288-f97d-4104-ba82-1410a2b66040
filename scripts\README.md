# Flutter 环境构建脚本

本目录包含用于构建不同环境版本的脚本文件。

## 脚本说明

### 运行脚本
- `run_dev.bat` - 运行开发环境版本
- `run_prod.bat` - 运行生产环境版本

### Android APK 构建脚本
- `build_apk_dev.bat` - 构建开发环境 APK
- `build_apk_prod.bat` - 构建生产环境 APK

### iOS 构建脚本
- `build_ios_dev.bat` - 构建开发环境 iOS
- `build_ios_prod.bat` - 构建生产环境 iOS

## 包名配置

### Android
- **开发环境**: `com.example.my_115_app.dev.debug` (debug模式)
- **生产环境**: `com.example.my_115_app` (release模式)

### iOS
- **开发环境**: `com.example.my115App.dev`
- **生产环境**: `com.example.my115App`

## 使用方法

1. 双击对应的 `.bat` 文件即可执行
2. 或者在命令行中运行：
   ```bash
   # 运行开发环境
   scripts\run_dev.bat
   
   # 构建生产APK
   scripts\build_apk_prod.bat
   ```

## 手动命令

如果需要手动执行，可以使用以下命令：

```bash
# 运行开发环境
flutter run --flavor development --target lib/main_development.dart

# 运行生产环境
flutter run --flavor production --target lib/main_production.dart --release

# 构建开发APK
flutter build apk --flavor development --target lib/main_development.dart --debug

# 构建生产APK
flutter build apk --flavor production --target lib/main_production.dart --release
```

## 注意事项

- iOS 构建完成后需要在 Xcode 中进行最终的打包和签名
- 确保已经正确配置了签名证书
- 开发和生产环境使用不同的包名，可以同时安装在同一设备上
